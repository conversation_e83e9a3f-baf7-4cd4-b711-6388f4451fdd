"use client"

import * as React from "react"
import { addDays, format } from "date-fns"
import { CalendarIcon } from "lucide-react"
import { DateRange } from "react-day-picker"
import { enUS, id } from 'date-fns/locale';
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { useLocale, useTranslations } from "next-intl"
import moment from "moment"

export function DatePickerWithRange({
  className,
  onUpdateDate,
  defaultDate
}: { onUpdateDate?: (data: DateRange | undefined) => void, defaultDate?: DateRange } & React.HTMLAttributes<HTMLDivElement>) {
  const t = useTranslations()
  const [date, setDate] = React.useState<DateRange | undefined>(defaultDate)
  const locale = useLocale()
  return (
    <div className={cn("grid gap-2 max-sm:w-full  ", className)}>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant={"outline"}
            className={cn(
              "md:w-[200px] justify-start text-left font-normal gap-2",
              !date && "text-muted-foreground"
            )}
          >
            <CalendarIcon className="w-4 h-4" />
            {date?.from ? (
              date.to ? (
                <>
                  {moment(date.from).format("DD MMM, YY")} -{" "}
                  {moment(date.to).format("DD MMM, YY")}
                </>
              ) : (
                moment(date.from).format("DD MMM, YY")
              )
            ) : (
              <span>{t('component.dataTable.dateRangeFIlter.placeholder')}</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            initialFocus
            mode="range"
            defaultMonth={date?.from}
            selected={date}
            onSelect={(e) => {
              onUpdateDate?.(e)
              setDate(e)
            }}
            numberOfMonths={2}
            locale={locale == "id" ? id : enUS}
          />
        </PopoverContent>
      </Popover>
    </div>
  )
}
