"use client"
import DialogDescriptionWrapper from "@/components/dialog-wrapper/dialog-description-wrapper"
import DialogHeaderWrapper from "@/components/dialog-wrapper/dialog-header-wrapper"
import DialogTitleWrapper from "@/components/dialog-wrapper/dialog-title-wrapper"
import DialogWrapper from "@/components/dialog-wrapper/dialog-wrapper"
import { useState } from "react"
import SubscriptionSignUpForm from "./form/subscription-sign-up.form"
import { useTranslations } from "next-intl"
import SubscriptionOtpForm from "./form/subscription-otp.form"
import { useSubscriptionSignUp } from "@/core/applications/mutations/subscription/use-subscription-sign-up"
import { PostSubscriptionDto, PostSubscriptionSignUpDto } from "@/core/infrastructures/subscription/dto"
import { useToast } from "@/hooks/use-toast"
import { Button } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"

export default function SubscriptionSignUpDialog({ customTrigger, priceId, productId }: { packageName: string, customTrigger: React.ReactNode, priceId: string, productId: string }) {
  const t = useTranslations("seeker")
  const { toast } = useToast()
  const [open, setOpen] = useState(false)
  const [isOtpStep, setIsOtpStep] = useState(false)
  const subscriptionMutation = useSubscriptionSignUp()
  const [registrationData, setRegistrationData] = useState<PostSubscriptionSignUpDto | null>(null)
  const handleSubscription = async () => {
    if (registrationData == null) return setIsOtpStep(false)
    try {
      const request = await subscriptionMutation.mutateAsync(registrationData)
      window.location.href = request.data.data.url
    } catch (error: any) {
      toast({
        title: t("message.subscriptionSignUp.failedToast.title"),
        description: error?.response?.data?.message || "",
        variant: "destructive"
      })
    }
  }
  const handleSubmitRegistrationData = (data: PostSubscriptionSignUpDto) => {
    setRegistrationData(data)
    setIsOtpStep(true)
  }
  return <DialogWrapper
    open={open}
    setOpen={setOpen}
    openTrigger={customTrigger}
  >
    <DialogHeaderWrapper className="mb-8 relative">
      {isOtpStep &&
        <Button size={"icon"} variant={"ghost"} className="top-0 left-0 absolute" onClick={() => setIsOtpStep(false)}>
          <ArrowLeft />
        </Button>
      }
      <div>
        <DialogTitleWrapper className="text-center">{t('subscription.signUp.title')}</DialogTitleWrapper>
        <DialogDescriptionWrapper className="text-center">{t('subscription.signUp.description')}</DialogDescriptionWrapper>
      </div>
    </DialogHeaderWrapper>
    {!isOtpStep ?
      <SubscriptionSignUpForm priceId={priceId} productId={productId} handleSubmit={handleSubmitRegistrationData} />
      :
      <SubscriptionOtpForm email={registrationData?.email || ""} onSuccess={async () => handleSubscription()} />
    }
  </DialogWrapper>
}