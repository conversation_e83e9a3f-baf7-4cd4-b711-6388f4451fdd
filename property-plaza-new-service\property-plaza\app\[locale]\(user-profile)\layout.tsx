import SeekersSidebar from "@/components/seeker-sidebar/sidebar";
import { SidebarProvider } from "@/components/ui/sidebar";
import SetupSeekersStore from "../(user)/setup-seekers";
import SeekersNavbar from "@/components/navbar/seekers-navbar-2";
import { cookies } from "next/headers";
import MainContentLayout from "@/components/seekers-content-layout/main-content-layout";

export default function UserProfileLayout({ children }: { children: React.ReactNode }) {
  const cookiesStore = cookies()
  const settingStore = cookiesStore.get("seekers-settings")?.value || ""
  const settings = settingStore ? JSON.parse(settingStore) : undefined
  const locale = cookiesStore.get('NEXT_LOCALE')?.value
  return <main className="overflow-hidden h-screen">

    <SetupSeekersStore />
    <div className="w-full sticky top-0 z-10 bg-white !mt-0" >
      <SeekersNavbar currency_={settings?.state?.currency} localeId={locale} />
    </div>
    <SidebarProvider className="h-[calc(100vh-113px)]">
      <MainContentLayout className="w-screen overflow-hidden">
        <div className="flex relative w-full h-full">
          <section>
            <SeekersSidebar />
          </section>
          <section className="flex-grow max-h-full overflow-auto max-sm:pb-16 pb-8">
            {children}
          </section>
        </div>
      </MainContentLayout>
    </SidebarProvider>
  </main>
}