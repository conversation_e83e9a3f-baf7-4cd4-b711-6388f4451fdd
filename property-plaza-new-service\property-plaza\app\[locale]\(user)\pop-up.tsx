"use client"

import FollowInstagramPopUp from "@/components/pop-up/follow-instagram-pop-up"
import { useRegisterStore } from "@/stores/register.store"
import { useEffect, useState } from "react"

export default function PopUp() {
  const { successSignUp, setSuccessSignUp, loading } = useRegisterStore()
  const [openFollowInstagramPopUp, setOpenFollowInstagramPopUp] = useState(false)
  const [firstTimeload, setFirstTimeLoad] = useState(true)
  useEffect(() => {
    if (loading) return
    if (!firstTimeload) return
    setFirstTimeLoad(false)
    setOpenFollowInstagramPopUp(successSignUp)
  }, [loading, successSignUp, firstTimeload])
  return <>
    <FollowInstagramPopUp
      open={openFollowInstagramPopUp}
      setOpen={(isOpen) => {
        setSuccessSignUp(isOpen)
        setOpenFollowInstagramPopUp(isOpen)
      }}
      trigger={<></>}
    />
  </>
}