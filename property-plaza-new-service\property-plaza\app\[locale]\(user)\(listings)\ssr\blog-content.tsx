import { useTranslations } from "next-intl";
import DefaultLayoutContent from "../../../../../components/seekers-content-layout/default-layout-content";
import BlogItem from "../blog-items";
import BlogDefaultImage from "@/public/blog-main-image.jpg"
import MainContentLayout from "@/components/seekers-content-layout/main-content-layout";
import { Sparkles } from "lucide-react";
import { getHomepagePosts } from "@/core/services/sanity/services";
import { getTranslations } from "next-intl/server"


export default async function BlogContent() {
  const blogs = await getHomepagePosts()

  const t = await getTranslations("seeker")

  return <article className="bg-seekers-foreground/50 py-12">
    <MainContentLayout>
      <DefaultLayoutContent title={
        <span className="inline-flex items-center gap-1">
          <Sparkles className="max-sm:hidden" /> {t('blog.sectionTitle')}
        </span>} className="!mt-2">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {blogs.map((item, idx) =>
            <BlogItem key={idx}
              date={item.publishedAt}
              content={item.metadata}
              title={item.title}
              image={item?.mainImage?.asset?.url || BlogDefaultImage}
              url={`/posts/${item.slug.current}`}
            />)
          }
        </div>
      </DefaultLayoutContent >
    </MainContentLayout>
  </article>
}