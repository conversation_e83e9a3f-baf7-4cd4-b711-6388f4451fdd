import { MetadataRoute } from "next";
import { getSSRSitemapPropertiesService } from "@/core/infrastructures/listing/service";
import { getPosts } from "@/core/services/sanity/services";
import {
  aboutUsUrl,
  contactUsUrl,
  noLoginPlanUrl,
  privacyOwnerUrl,
  privacySeekerUrl,
  searchUrl,
  termSeekerUrl,
  userDataDeletionUrl,
} from "@/lib/constanta/route";

export const dynamic = "force-dynamic";

function slugify(text: string): string {
  return text
    .toString()
    .normalize("NFD") // Normalize accented characters
    .replace(/[\u0300-\u036f]/g, "") // Remove diacritics
    .toLowerCase()
    .trim()
    .replace(/[^a-z0-9]+/g, "-") // Replace non-alphanumeric with hyphens
    .replace(/^-+|-+$/g, ""); // Trim hyphens from start and end
}

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = "https://www.property-plaza.com";
  const now = new Date().toISOString();

  // Get blog posts
  const blogPosts = await getPosts();
  const blogs: MetadataRoute.Sitemap = [];
  if (blogPosts) {
    blogPosts.forEach((item) => {
      blogs.push({
        url: `${baseUrl}/en/posts/${item.slug.current}`,
        priority: 0.7,
        changeFrequency: "monthly",
        lastModified: item.publishedAt
          ? new Date(item.publishedAt).toISOString()
          : now,
        alternates: {
          languages: {
            en: `${baseUrl}/en/posts/${item.slug.current}`,
            id: `${baseUrl}/id/posts/${item.slug.current}`,
            "x-default": `${baseUrl}/posts/${item.slug.current}`,
          },
        },
      });
      blogs.push({
        url: `${baseUrl}/id/posts/${item.slug.current}`,
        priority: 0.7,
        changeFrequency: "monthly",
        lastModified: item.publishedAt
          ? new Date(item.publishedAt).toISOString()
          : now,
        alternates: {
          languages: {
            en: `${baseUrl}/en/posts/${item.slug.current}`,
            id: `${baseUrl}/id/posts/${item.slug.current}`,
            "x-default": `${baseUrl}/posts/${item.slug.current}`,
          },
        },
      });
      blogs.push({
        url: `${baseUrl}/posts/${item.slug.current}`,
        priority: 0.7,
        changeFrequency: "monthly",
        lastModified: item.publishedAt
          ? new Date(item.publishedAt).toISOString()
          : now,
        alternates: {
          languages: {
            en: `${baseUrl}/en/posts/${item.slug.current}`,
            id: `${baseUrl}/id/posts/${item.slug.current}`,
            "x-default": `${baseUrl}/posts/${item.slug.current}`,
          },
        },
      });
    });
  }

  // Get property listings (uncommented and fixed)
  const seekerListing = await getSSRSitemapPropertiesService();
  const properties: MetadataRoute.Sitemap = [];
  if (seekerListing.data) {
    seekerListing.data.forEach((item) => {
      const title = slugify(item.title);
      properties.push({
        url: `${baseUrl}/en/${title}?code=${item.id}`,
        priority: 0.9,
        changeFrequency: "monthly",
        lastModified: now,
        alternates: {
          languages: {
            en: `${baseUrl}/en/${title}?code=${item.id}`,
            id: `${baseUrl}/id/${title}?code=${item.id}`,
            "x-default": `${baseUrl}/${title}?code=${item.id}`,
          },
        },
      });
      properties.push({
        url: `${baseUrl}/id/${title}?code=${item.id}`,
        priority: 0.9,
        changeFrequency: "monthly",
        lastModified: now,
        alternates: {
          languages: {
            en: `${baseUrl}/en/${title}?code=${item.id}`,
            id: `${baseUrl}/id/${title}?code=${item.id}`,
            "x-default": `${baseUrl}/${title}?code=${item.id}`,
          },
        },
      });
      properties.push({
        url: `${baseUrl}/${title}?code=${item.id}`,
        priority: 0.9,
        changeFrequency: "monthly",
        lastModified: now,
        alternates: {
          languages: {
            en: `${baseUrl}/en/${title}?code=${item.id}`,
            id: `${baseUrl}/id/${title}?code=${item.id}`,
            "x-default": `${baseUrl}/${title}?code=${item.id}`,
          },
        },
      });
    });
  }

  // Static pages sitemap
  const staticPages: MetadataRoute.Sitemap = [
    {
      url: `${baseUrl}/en`,
      changeFrequency: "yearly",
      priority: 1,
      lastModified: now,
      alternates: {
        languages: {
          en: `${baseUrl}/en`,
          id: `${baseUrl}/id`,
          "x-default": `${baseUrl}`,
        },
      },
    },
    {
      url: `${baseUrl}/id`,
      changeFrequency: "yearly",
      priority: 1,
      lastModified: now,
      alternates: {
        languages: {
          en: `${baseUrl}/en`,
          id: `${baseUrl}/id`,
          "x-default": `${baseUrl}`,
        },
      },
    },
    {
      url: `${baseUrl}`,
      changeFrequency: "yearly",
      priority: 1,
      lastModified: now,
      alternates: {
        languages: {
          en: `${baseUrl}/en`,
          id: `${baseUrl}/id`,
          "x-default": `${baseUrl}`,
        },
      },
    },
    {
      url: `${baseUrl}/en${noLoginPlanUrl}`,
      changeFrequency: "yearly",
      priority: 0.8,
      lastModified: now,
      alternates: {
        languages: {
          en: `${baseUrl}/en${noLoginPlanUrl}`,
          id: `${baseUrl}/id${noLoginPlanUrl}`,
          "x-default": `${baseUrl}${noLoginPlanUrl}`,
        },
      },
    },
    {
      url: `${baseUrl}/id${noLoginPlanUrl}`,
      changeFrequency: "yearly",
      priority: 0.8,
      lastModified: now,
      alternates: {
        languages: {
          en: `${baseUrl}/en${noLoginPlanUrl}`,
          id: `${baseUrl}/id${noLoginPlanUrl}`,
          "x-default": `${baseUrl}${noLoginPlanUrl}`,
        },
      },
    },
    {
      url: `${baseUrl}${noLoginPlanUrl}`,
      changeFrequency: "yearly",
      priority: 0.8,
      lastModified: now,
      alternates: {
        languages: {
          en: `${baseUrl}/en${noLoginPlanUrl}`,
          id: `${baseUrl}/id${noLoginPlanUrl}`,
          "x-default": `${baseUrl}${noLoginPlanUrl}`,
        },
      },
    },
    {
      url: `${baseUrl}/en${aboutUsUrl}`,
      changeFrequency: "yearly",
      priority: 0.8,
      lastModified: now,
      alternates: {
        languages: {
          en: `${baseUrl}/en${aboutUsUrl}`,
          id: `${baseUrl}/id${aboutUsUrl}`,
          "x-default": `${baseUrl}${aboutUsUrl}`,
        },
      },
    },
    {
      url: `${baseUrl}/id${aboutUsUrl}`,
      changeFrequency: "yearly",
      priority: 0.8,
      lastModified: now,
      alternates: {
        languages: {
          en: `${baseUrl}/en${aboutUsUrl}`,
          id: `${baseUrl}/id${aboutUsUrl}`,
          "x-default": `${baseUrl}${aboutUsUrl}`,
        },
      },
    },
    {
      url: `${baseUrl}${aboutUsUrl}`,
      changeFrequency: "yearly",
      priority: 0.8,
      lastModified: now,
      alternates: {
        languages: {
          en: `${baseUrl}/en${aboutUsUrl}`,
          id: `${baseUrl}/id${aboutUsUrl}`,
          "x-default": `${baseUrl}${aboutUsUrl}`,
        },
      },
    },
    {
      url: `${baseUrl}/en${contactUsUrl}`,
      changeFrequency: "yearly",
      priority: 0.5,
      lastModified: now,
      alternates: {
        languages: {
          en: `${baseUrl}/en${contactUsUrl}`,
          id: `${baseUrl}/id${contactUsUrl}`,
          "x-default": `${baseUrl}${contactUsUrl}`,
        },
      },
    },
    {
      url: `${baseUrl}/id${contactUsUrl}`,
      changeFrequency: "yearly",
      priority: 0.5,
      lastModified: now,
      alternates: {
        languages: {
          en: `${baseUrl}/en${contactUsUrl}`,
          id: `${baseUrl}/id${contactUsUrl}`,
          "x-default": `${baseUrl}${contactUsUrl}`,
        },
      },
    },
    {
      url: `${baseUrl}${contactUsUrl}`,
      changeFrequency: "yearly",
      priority: 0.5,
      lastModified: now,
      alternates: {
        languages: {
          en: `${baseUrl}/en${contactUsUrl}`,
          id: `${baseUrl}/id${contactUsUrl}`,
          "x-default": `${baseUrl}${contactUsUrl}`,
        },
      },
    },
    {
      url: `${baseUrl}/en${privacySeekerUrl}`,
      changeFrequency: "yearly",
      priority: 0.3,
      lastModified: now,
      alternates: {
        languages: {
          en: `${baseUrl}/en${privacySeekerUrl}`,
          id: `${baseUrl}/id${privacySeekerUrl}`,
          "x-default": `${baseUrl}${privacySeekerUrl}`,
        },
      },
    },
    {
      url: `${baseUrl}/id${privacySeekerUrl}`,
      changeFrequency: "yearly",
      priority: 0.3,
      lastModified: now,
      alternates: {
        languages: {
          en: `${baseUrl}/en${privacySeekerUrl}`,
          id: `${baseUrl}/id${privacySeekerUrl}`,
          "x-default": `${baseUrl}${privacySeekerUrl}`,
        },
      },
    },
    {
      url: `${baseUrl}${privacySeekerUrl}`,
      changeFrequency: "yearly",
      priority: 0.3,
      lastModified: now,
      alternates: {
        languages: {
          en: `${baseUrl}/en${privacySeekerUrl}`,
          id: `${baseUrl}/id${privacySeekerUrl}`,
          "x-default": `${baseUrl}${privacySeekerUrl}`,
        },
      },
    },
    {
      url: `${baseUrl}/en${userDataDeletionUrl}`,
      changeFrequency: "yearly",
      priority: 0.3,
      lastModified: now,
      alternates: {
        languages: {
          en: `${baseUrl}/en${userDataDeletionUrl}`,
          id: `${baseUrl}/id${userDataDeletionUrl}`,
          "x-default": `${baseUrl}${userDataDeletionUrl}`,
        },
      },
    },
    {
      url: `${baseUrl}/id${userDataDeletionUrl}`,
      changeFrequency: "yearly",
      priority: 0.3,
      lastModified: now,
      alternates: {
        languages: {
          en: `${baseUrl}/en${userDataDeletionUrl}`,
          id: `${baseUrl}/id${userDataDeletionUrl}`,
          "x-default": `${baseUrl}${userDataDeletionUrl}`,
        },
      },
    },
    {
      url: `${baseUrl}${userDataDeletionUrl}`,
      changeFrequency: "yearly",
      priority: 0.3,
      lastModified: now,
      alternates: {
        languages: {
          en: `${baseUrl}/en${userDataDeletionUrl}`,
          id: `${baseUrl}/id${userDataDeletionUrl}`,
          "x-default": `${baseUrl}${userDataDeletionUrl}`,
        },
      },
    },
    {
      url: `${baseUrl}/en${termSeekerUrl}`,
      changeFrequency: "yearly",
      priority: 0.3,
      lastModified: now,
      alternates: {
        languages: {
          en: `${baseUrl}/en${termSeekerUrl}`,
          id: `${baseUrl}/id${termSeekerUrl}`,
          "x-default": `${baseUrl}${termSeekerUrl}`,
        },
      },
    },
    {
      url: `${baseUrl}/id${termSeekerUrl}`,
      changeFrequency: "yearly",
      priority: 0.3,
      lastModified: now,
      alternates: {
        languages: {
          en: `${baseUrl}/en${termSeekerUrl}`,
          id: `${baseUrl}/id${termSeekerUrl}`,
          "x-default": `${baseUrl}${termSeekerUrl}`,
        },
      },
    },
    {
      url: `${baseUrl}${termSeekerUrl}`,
      changeFrequency: "yearly",
      priority: 0.3,
      lastModified: now,
      alternates: {
        languages: {
          en: `${baseUrl}/en${termSeekerUrl}`,
          id: `${baseUrl}/id${termSeekerUrl}`,
          "x-default": `${baseUrl}${termSeekerUrl}`,
        },
      },
    },
    {
      url: `${baseUrl}/en${searchUrl}/all`,
      changeFrequency: "yearly",
      priority: 0.3,
      lastModified: now,
      alternates: {
        languages: {
          en: `${baseUrl}/en${searchUrl}/all`,
          id: `${baseUrl}/id${searchUrl}/all`,
          "x-default": `${baseUrl}${searchUrl}/all`,
        },
      },
    },
    {
      url: `${baseUrl}/id${searchUrl}/all`,
      changeFrequency: "yearly",
      priority: 0.3,
      lastModified: now,
      alternates: {
        languages: {
          en: `${baseUrl}/en${searchUrl}/all`,
          id: `${baseUrl}/id${searchUrl}/all`,
          "x-default": `${baseUrl}${searchUrl}/all`,
        },
      },
    },
    {
      url: `${baseUrl}${searchUrl}/all`,
      changeFrequency: "yearly",
      priority: 0.3,
      lastModified: now,
      alternates: {
        languages: {
          en: `${baseUrl}/en${searchUrl}/all`,
          id: `${baseUrl}/id${searchUrl}/all`,
          "x-default": `${baseUrl}${searchUrl}/all`,
        },
      },
    },
  ];

  // Combine all sitemap entries
  return [...staticPages, ...properties, ...blogs];
}
