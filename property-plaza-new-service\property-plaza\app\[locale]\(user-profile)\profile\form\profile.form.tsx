"use client"
import { z } from "zod"
import useProfileFormSchema from "./profile-form.schema"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { useUserStore } from "@/stores/user.store"
import { useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { useTranslations } from "next-intl"
import DefaultInput from "@/components/input-form/default-input"
import EmailInput from "@/components/input-form/email-input"
import { Form } from "@/components/ui/form"
import PhoneNumberInput from "@/components/input-form/phone-number-input"
import { UpdateUserDto } from "@/core/infrastructures/user/dto"
import { useUpadeUserDetail } from "@/core/applications/mutations/user/use-update-user-detail"
import libPhoneNumber from "google-libphonenumber"
import phone from "phone"
import ChangeContact from "../change-contact"

const phoneUtil = libPhoneNumber.PhoneNumberUtil.getInstance()


export default function ProfileForm() {
  const formSchema = useProfileFormSchema()
  const t = useTranslations("seeker")
  const { email, phoneCode, phoneNumber, code, accounts: { firstName, lastName } } = useUserStore(state => state.seekers)
  type typeFormSchema = z.infer<typeof formSchema>
  const useUpdateUserMutation = useUpadeUserDetail()

  const form = useForm<typeFormSchema>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      id: "",
      firstName: "",
      lastName: "",
      email: "",
      phoneNumber: "",
    },
  })
  useEffect(() => {
    if (email) {
      form.setValue("email", email)
    }
    if (code) {
      form.setValue("id", code)
    }
    if (firstName) {

      form.setValue("firstName", firstName)
    }
    if (lastName) {
      form.setValue("lastName", lastName)
    }
    if (phoneCode && phoneNumber) {
      form.setValue("phoneNumber", phoneCode + phoneNumber)
    }
  }, [email, firstName, lastName, phoneNumber, phoneCode, code, form])
  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    const phoneNumber = phone(values.phoneNumber)

    const data: UpdateUserDto = {
      phone_number: phoneNumber.phoneNumber?.replace(phoneNumber.countryCode, ""),
      phone_code: phoneNumber.countryCode || "",
      first_name: values.firstName,
      last_name: values.lastName
    }

    const response = await useUpdateUserMutation.mutateAsync(data)
  }
  return <Form {...form}>
    <form onSubmit={form.handleSubmit(onSubmit)} >
      <div className="grid max-sm:grid-cols-1 md:grid-cols-2 gap-6">
        <DefaultInput
          form={form}
          label={t('form.label.firstName')}
          name='firstName'
          placeholder={t("form.placeholder.basePlaceholder",
            { field: t("form.field.firstName") })}
          type="string"
        />
        <DefaultInput
          form={form}
          label={t('form.label.lastName')}
          name='lastName'
          placeholder={t("form.placeholder.basePlaceholder", { field: t("form.field.lastName") })}
          type="string"
        />
        <div className="flex gap-2 justify-between items-end">
          <EmailInput
            form={form}
            label={t("form.label.email")}
            name="email"
            placeholder=""
            isEditable={false}

            inputProps={{
              className: "min-w-full max-w-full"
            }}
          />
          {/* <ChangeContact
            onChange={(val) => form.setValue("email", val)}
            currentVal={form.getValues("email")}
            trigger={
              <Button type="button" variant={"outline"} className="shadow-none h-[34px] md:h-[38px]">
                {t("cta.change")}
              </Button>
            }
            type="email"
          /> */}
        </div>
        <div className="flex gap-2 items-end">
          <PhoneNumberInput
            form={form}
            label={t("form.label.phoneNumber")}
            name="phoneNumber"
            placeholder=""
            isEditable={false}
            inputProps={{
              className: "inputProps"
            }}
          />
          <ChangeContact
            onChange={(val) => form.setValue("phoneNumber", val)}
            currentVal={form.getValues("phoneNumber")}
            trigger={
              <Button type="button" variant={"outline"} className="shadow-none h-[34px] md:h-[38px]">
                {t("cta.change")}
              </Button>
            }
            type="phone"
          />
        </div>
      </div>
      <Button
        className="my-8 min-w-40 "
        variant={"default-seekers"}
        type="submit"
        loading={useUpdateUserMutation.isPending}
      >{t("cta.saveChanges")}</Button>
    </form>
  </Form>
}