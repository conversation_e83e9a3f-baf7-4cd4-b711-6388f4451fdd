"use client";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { FilterPriceDistribution } from "@/core/domain/listing/listing-seekers";
import { cn, formatCurrency, formatNumber } from "@/lib/utils";
import { RangeFilter } from "@/stores/seeker-filter.store";
import { Minus } from "lucide-react";
import { ComponentProps, useState, useEffect, useCallback, useMemo } from "react";
import PriceDistributionChart from "./price-distribution-chart";
import { debounce } from "lodash";
import { useLocale, useTranslations } from "next-intl";
import BaseRangeInputSeekers from "@/components/input-form/base-range-input-seekers";
import { useDebounce } from "@/hooks/use-debounce";
import { useSeekersSettingsStore } from "@/stores/seekers-settings.store";

export const MAXIMUM_VALUE = 50_000_000;

interface PriceRangeSliderItemProps extends ComponentProps<typeof Slider> {
  min?: number;
  max?: number;
  rangeValue: RangeFilter;
  isUsingChart?: boolean;
  chartValues?: FilterPriceDistribution[];
  conversions?: { [key: string]: number },
  usingCurrency?: boolean

  onRangeValueChange: (minValue: number, maxValue: number) => void;
}

export default function RangeSliderItem({
  max = MAXIMUM_VALUE,
  min = 0,
  onRangeValueChange,
  rangeValue,
  className,
  isUsingChart,
  chartValues,
  conversions,
  ...rest
}: PriceRangeSliderItemProps) {
  const t = useTranslations("seeker")
  // Consolidate slider range values into one state
  const [numRangeValue, setNumRangeValue] = useState<[number, number]>([rangeValue.min, rangeValue.max]);
  const [minNum, setMinNum] = useState(formatNumber(rangeValue.min))
  const [maxNum, setMaxNum] = useState(formatNumber(rangeValue.max))
  const { currency } = useSeekersSettingsStore()
  const locale = useLocale()
  useEffect(() => {
    setMinNum(formatNumber(rangeValue.min))
    setMaxNum(formatNumber(rangeValue.max))
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [rangeValue])

  const minNumDebounce = useDebounce(minNum)
  const maxNumDebounce = useDebounce(maxNum)
  const handleChangeMinValueDummy = (val: string) => {
    const minPrice = parseFloat(val.replaceAll(/[^0-9.-]/g, "") || "0");
    const formattedMinPrice = formatNumber(minPrice)
    setMinNum(formattedMinPrice)
  }

  const handleChangeMaxValueDummy = (val: string) => {
    const maxPrice = parseFloat(val.replaceAll(/[^0-9.-]/g, "") || "0");
    const formattedMaxPrice = formatNumber(maxPrice)
    setMaxNum(formattedMaxPrice)
  }
  const handleChangeSliderValue = (val: number[]) => {
    handleChangeMinValueDummy(val[0].toString())
    handleChangeMaxValueDummy(val[1].toString())
    setNumRangeValue([val[0], val[1]]);
  }
  const handleBlurInputValue = (val: string, type: "min" | "max", thresholdValue: { min: number, max: number }) => {
    const rawVal = parseFloat(val.replaceAll(/[^0-9.-]/g, "") || "0");

    if (type == "min" && rawVal < thresholdValue.min) {
      const minThresholdVal = formatNumber(thresholdValue.min)
      setMinNum(minThresholdVal)
      return
    } else if (type == "min" && rawVal >= thresholdValue.max) {
      const threshold = formatNumber(thresholdValue.max * 0.9)
      setMinNum(threshold)
    } else if (type == "max" && (rawVal >= thresholdValue.max || rawVal <= thresholdValue.min)) {
      const threshold = formatNumber(thresholdValue.max)
      setMaxNum(threshold)
    }
  }

  useEffect(() => {
    const minPrice = parseFloat(minNumDebounce.replaceAll(/[^0-9.-]/g, "") || "0");
    const maxPrice = parseFloat(maxNumDebounce.replaceAll(/[^0-9.-]/g, "") || "0");

    onRangeValueChange(minPrice, maxPrice)
    setNumRangeValue([minPrice, maxPrice])
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [minNumDebounce, maxNumDebounce])

  return (
    <div className="w-full space-y-2">
      <div className="-space-y-1">
        {isUsingChart && (
          <div className="relative isolate">
            <PriceDistributionChart range={numRangeValue} data={chartValues || []} />
          </div>
        )}
        <BaseRangeInputSeekers
          value={numRangeValue}
          max={max}
          min={min}
          onValueChange={(val: number[]) => handleChangeSliderValue(val)}
        />
      </div>

      <div className="flex justify-between gap-2 items-center">
        <div className="flex-1 border rounded-sm p-3">
          <Label className="font-normal text-xs text-seekers-text">{t('misc.minimum')}</Label>
          <Input
            max={max}
            min={min}
            value={minNum}
            className="border-none p-0 h-fit text-base font-medium"
            onChange={(e) => handleChangeMinValueDummy(e.target.value)}
            onBlur={e => handleBlurInputValue(e.target.value, "min", { min, max })}
          />
        </div>

        <div>
          <Label className="text-background fot-normal text-[10px]"></Label>
          <Minus />
        </div>

        <div className="flex-1 border rounded-sm p-3">
          <Label className="font-normal text-[10px]">{t('form.label.maximum')}</Label>
          <Input
            max={max}
            min={min}
            className="border-none p-0 h-fit text-base font-medium"
            value={maxNum}
            onChange={(e) => handleChangeMaxValueDummy(e.target.value)}
            onBlur={(e) => {
              handleBlurInputValue(e.target.value, "max", { min, max })
            }}
          />
        </div>
      </div>
    </div>
  );
}
