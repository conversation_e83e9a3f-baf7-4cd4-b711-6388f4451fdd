import { MAX_MESSAGE_COUNT, MIN_MESSAGE_COUNT } from "@/lib/constanta/constant";
import { useTranslations } from "next-intl";
import { z } from "zod";

export default function useChatWithCsFormSchema(){
  const t = useTranslations("seeker")
  const formSchema = z.object({
    text: z.string({message: t("form.utility.fieldRequired", {field: t("form.field.message")})})
    .min(MIN_MESSAGE_COUNT, {
      message: t("form.utility.minimumLength", {field: t("form.field.message"), length: MIN_MESSAGE_COUNT})
    }).max(MAX_MESSAGE_COUNT, {message: t("form.utility.maximumLength", {field: t("form.field.message"), length: MAX_MESSAGE_COUNT})})
  })
  return formSchema
}