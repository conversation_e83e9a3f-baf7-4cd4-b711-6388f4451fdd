import { useSeekerFilterStore } from "@/stores/seekers-filter-2.store";
import FilterContentLayout from "./filter-content-layout";
import PriceRangeSliderItem from "./range-slider-item";
import { useTranslations } from "next-intl";
import { FilterPriceDistribution } from "@/core/domain/listing/listing-seekers";
import { useGetFilterParameter } from "@/core/applications/queries/listing/use-get-filter-parameters";
import { Skeleton } from "@/components/ui/skeleton";
import { useEffect, useState } from "react";
import useSearchParamWrapper from "@/hooks/use-search-param-wrapper";
import { filterTitles } from "@/lib/constanta/constant";
import { useSeekersSettingsStore } from "@/stores/seekers-settings.store";


export function generatePriceDistribution(
  minPrice: number,
  maxPrice: number,
  count: number
): FilterPriceDistribution[] {
  if (minPrice >= maxPrice) {
    throw new Error("minPrice should be less than maxPrice.");
  }

  if (count <= 0) {
    throw new Error("Count should be greater than 0.");
  }

  const randomPriceDistribution: FilterPriceDistribution[] = [];

  for (let i = 0; i < count; i++) {
    const randomPrice = (Math.random() * (maxPrice - minPrice) + minPrice).toFixed(2); // Generate random price
    const randomAmount = (Math.random() * 100).toFixed(0); // Generate random amount (you can customize this range)

    randomPriceDistribution.push({
      price: randomPrice,
      amount: randomAmount,
    });
  }
  const orderedRandomPriceDistribution = randomPriceDistribution.sort((a, b) => parseFloat(a.price) - parseFloat(b.price))
  return orderedRandomPriceDistribution;
}
export default function PriceRange({ conversions }: { conversions: { [key: string]: number } }) {
  const t = useTranslations("seeker")
  const { priceRange, setPriceRange } = useSeekerFilterStore()
  const filterParameterQuery = useGetFilterParameter()
  const [dummyData, setDummyData] = useState<FilterPriceDistribution[]>([])
  const { searchParams } = useSearchParamWrapper()
  const maxPriceParams = searchParams.get(filterTitles.maxPrice)
  const minPriceParams = searchParams.get(filterTitles.minPrice)
  const { currency } = useSeekersSettingsStore()
  const [minPrice, setMinPrice] = useState(0)
  const [maxPrice, setMaxPrice] = useState(0)
  useEffect(() => {
    if (filterParameterQuery.isPending) return
    const priceRangeValue = filterParameterQuery.data?.data?.priceRange
    const currencyRate = conversions[currency] || 1
    // price need to be converted to same with currency
    if (priceRangeValue) {
      const minPriceRange = +(minPriceParams || priceRangeValue.min) * currencyRate
      const maxPriceRange = +(maxPriceParams || priceRangeValue.max) * currencyRate
      setPriceRange(minPriceRange, maxPriceRange)
      setMinPrice(priceRangeValue.min * currencyRate)
      setMaxPrice(priceRangeValue.max * currencyRate)
      const distributionPriceChart = generatePriceDistribution(minPriceRange, maxPriceRange, 100)
      setDummyData(distributionPriceChart)
    }


  }, [filterParameterQuery.isPending, filterParameterQuery.data?.data?.priceRange, setPriceRange, minPriceParams, maxPriceParams, conversions, currency])


  return <FilterContentLayout title={t('listing.filter.priceRange.title')} description={t('listing.filter.priceRange.description')}>
    {filterParameterQuery.isPending ? <>
      <Skeleton className="w-full h-24" />
      <div className="flex gap-4">
        <Skeleton className="w-full h-16" />
        <Skeleton className="w-full h-16" />
      </div>
    </> :
      <PriceRangeSliderItem
        rangeValue={priceRange}
        onRangeValueChange={setPriceRange}
        chartValues={dummyData}
        isUsingChart
        min={minPrice}
        max={maxPrice}
      />
    }
  </FilterContentLayout>
}