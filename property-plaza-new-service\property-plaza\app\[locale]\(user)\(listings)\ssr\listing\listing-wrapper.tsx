"use client"
import { ListingListSeekers } from "@/core/domain/listing/listing-seekers";
import { ComponentProps } from "react";
import { handleClickListing } from "../utils";

export default function ListingWrapper({ listing, ...rest }: { listing: ListingListSeekers } & ComponentProps<"div">) {
  return <div {...rest} onClick={e => {
    e.stopPropagation()
    handleClickListing(listing.title, listing.code)
  }}>
    {rest.children}
  </div>
}