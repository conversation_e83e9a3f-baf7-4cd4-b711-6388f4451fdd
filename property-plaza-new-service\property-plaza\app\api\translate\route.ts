import { NextResponse } from "next/server";
const baseTranslationUrl =
  "https://translation.googleapis.com/language/translate/v2";

export async function POST(req: Request) {
  try {
    // Parse the request body
    const { text } = await req.json();

    if (!text) {
      return NextResponse.json({ error: "Text is required" }, { status: 400 });
    }
    // detect language
    const detectedLanguage = await fetch(
      `${baseTranslationUrl}/detect?q=${text}&key=${process.env.GOOGLE_TRANSLATE_API}`,
      { method: "POST" }
    )
      .then((data) => data.json())
      .then((data) => data.data);

    const target =
      detectedLanguage.detections[0][0].language == "en" ? "id" : "en";
    // Perform the translation
    const data = await fetch(
      `${baseTranslationUrl}?q=${text}&target=${target}&key=${process.env.GOOGLE_TRANSLATE_API}`,
      {
        method: "POST",
      }
    );
    const result = await data.json();
    const translation = result.data.translations[0];

    // Return the translated text
    return NextResponse.json({
      translatedText: translation.translatedText,
      translatedFrom: detectedLanguage.detections[0][0].language,
    });
  } catch (error: any) {
    // Check if it's an API authentication error
    if (error.code === 403) {
      return NextResponse.json(
        { error: "API authentication failed. Please check your API key." },
        { status: 403 }
      );
    }

    return NextResponse.json({ error: "Translation failed" }, { status: 500 });
  }
}
