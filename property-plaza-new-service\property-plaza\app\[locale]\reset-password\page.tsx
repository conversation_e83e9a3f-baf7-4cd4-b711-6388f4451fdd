import { getTranslations } from "next-intl/server"
import Content from "./content"
import { BaseMetadataProps } from "@/types/base"
import { Metadata } from "next"
export async function generateMetadata({ params, searchParams }: BaseMetadataProps<{}>): Promise<Metadata> {
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const t = await getTranslations("seeker")
  return {
    title: t('metadata.rootLayout.title'),
    description: t('metadata.rootLayout.description'),
    alternates: {
      languages: {
        "id": process.env.USER_DOMAIN + "/id",
        "en": process.env.USER_DOMAIN + "/en",
        "x-default": process.env.USER_DOMAIN + "/en",
      },
      canonical: {
        url: "https://property-plaza.com"
      },
    },
    robots: {
      index: false,
      follow: false
    },
  }
}
export default function ResetPasswordPage() {

  return (
    <div className="container flex items-center justify-center min-h-screen py-10">
      <div className="max-w-sm max-sm:p-4">
        <Content />
      </div>
    </div>
  )
}