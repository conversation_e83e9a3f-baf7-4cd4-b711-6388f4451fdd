"use client"

import DialogWrapper from "@/components/dialog-wrapper/dialog-wrapper";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import SeekersProfile from "@/components/navbar/seekers-profile";
import DialogHeaderWrapper from "@/components/dialog-wrapper/dialog-header-wrapper";
import { SeekerLoginForm } from "./seekers-login.form";
import { UserSignUpForm } from "./seekers-sign-up.form";
import { Button } from "@/components/ui/button";
import { ChevronLeft } from "lucide-react";
import SeekersOtpForm from "./seekers.otp.form";
import SeekersResetPasswordForm from "./seekers-reset-password.form"
import { cn } from "@/lib/utils";

const AUTH_STEP = {
  signUp: "SIGN_UP",
  login: "LOGIN",
  otp: "OTP",
  resetPassword: "RESET_PASSWORD"
} as const
type AuthStepType = typeof AUTH_STEP[keyof typeof AUTH_STEP]
export default function SeekerAuthDialog({ triggerClassName, customTrigger }: { triggerClassName?: string, customTrigger?: React.ReactNode }) {
  const t = useTranslations("seeker")

  const [open, setOpen] = useState(false)
  const [title, setTitle] = useState("")
  const [step, setStep] = useState<AuthStepType>(AUTH_STEP.signUp)
  useEffect(() => {
    switch (step) {
      case AUTH_STEP.signUp:
        setTitle(t('form.title.signUp'))
        return
      case AUTH_STEP.login:
        setTitle(t('form.title.login'))
        return
      case AUTH_STEP.otp:
        setTitle(t('form.title.enterOtpCode'))
        return
      case AUTH_STEP.resetPassword:
        setTitle(t('form.title.resetPassword'))
        return
      default:
        return
    }
  }, [step, t])
  return <DialogWrapper
    open={open}
    setOpen={setOpen}
    openTrigger={customTrigger || <button className={`border relative border-seekers-text-lighter shadow-md rounded-full h-10 w-14 !bg-seekers-text-light ${triggerClassName}`}>
      <SeekersProfile url={""} />
    </button>}
    dialogClassName="w-full sm:max-w-[500px] p-6"
  >
    <DialogHeaderWrapper className="flex flex-col space-y-1.5 text-center mb-6">
      {(step == AUTH_STEP.otp || step == AUTH_STEP.resetPassword) && (
        <Button
          variant={"ghost"}
          size={"icon"}
          className="absolute top-4 left-4"
          onClick={() => setStep(step == AUTH_STEP.otp ? AUTH_STEP.signUp : AUTH_STEP.login)}
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>
      )}
      <section className="space-y-1.5">
        <h2 className="tracking-tight text-center text-2xl font-bold max-w-xs mx-auto">{title}</h2>
        <p className={cn(step === AUTH_STEP.otp ? "hidden" : "", "text-sm text-muted-foreground text-center")}>
          {step === AUTH_STEP.login ? t('auth.login.subtitle') :
            step === AUTH_STEP.signUp ? t('auth.register.subtitle') :
              step === AUTH_STEP.resetPassword ? t('auth.resetPassword.subtitle') : ""}
        </p>

      </section>
    </DialogHeaderWrapper>

    {
      step == AUTH_STEP.login ? <SeekerLoginForm
        onClickSignUp={() => setStep(AUTH_STEP.signUp)}
        onClickResetPassword={() => setStep(AUTH_STEP.resetPassword)}
      />
        :
        step == AUTH_STEP.signUp ?
          <UserSignUpForm onSuccess={() => setStep(AUTH_STEP.otp)} onClickLogin={() => setStep(AUTH_STEP.login)} />
          :
          step == AUTH_STEP.otp ? <section>
            {
              step === AUTH_STEP.otp && <div className="text-seekers-text-light">
                <p >{t('auth.otp.content.title')}</p>
                <ul className="list-disc list-inside">
                  <li>{t('auth.otp.item.one')}</li>
                  <li>{t('auth.otp.item.two')}</li>
                  <li>{t('auth.otp.item.three')}</li>
                </ul>
                <p>{t('auth.otp.content.cantFindEmail')}</p>
              </div>
            }
            <SeekersOtpForm />
          </section>
            :
            step == AUTH_STEP.resetPassword ? (
              <SeekersResetPasswordForm onBack={() => setStep(AUTH_STEP.login)} />
            ) : null
    }
  </DialogWrapper>
}