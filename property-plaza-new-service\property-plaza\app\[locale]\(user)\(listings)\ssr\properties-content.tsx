import DefaultLayoutContent from "@/components/seekers-content-layout/default-layout-content";
import { ListingListSeekers } from "@/core/domain/listing/listing-seekers";
import ListingItem from "./listing/listing-item";
import ListingImage from "./listing/listing-image";
import { ListingTitle } from "./listing/listing-title";
import { ListingLocation } from "./listing/listing-location";
import { ListingPrice } from "./listing/listing-price";
import { cookies } from "next/headers";
import { SeekersSettings } from "@/stores/seekers-settings.store";

export default async function PropertiesContent({ title, data, forceLazyLoad = false, conversions }: { title: string, data: ListingListSeekers[], forceLazyLoad?: boolean, conversions: { [key: string]: number } }) {
  const cookiesStore = cookies()
  const setting: string | undefined = cookiesStore.get("seekers-settings")?.value
  const currency: SeekersSettings | undefined = setting ? JSON.parse(setting)?.state : undefined
  const locale: string | undefined = cookiesStore.get('NEXT_LOCALE')?.value

  return <DefaultLayoutContent title={title}>
    <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-3">
      {data.map((item, idx) => <ListingItem key={idx} >
        <ListingImage listing={item} heartSize="large" forceLazyLoad={forceLazyLoad} />
        <div className="space-y-0">
          <ListingTitle listing={item} />
          <ListingLocation listing={item} />
        </div>
        <ListingPrice listing={item} currency={currency?.currency || "EUR"} locale={locale || "en"} conversions={conversions} />
      </ListingItem>
      )}
    </div>
  </DefaultLayoutContent>
}