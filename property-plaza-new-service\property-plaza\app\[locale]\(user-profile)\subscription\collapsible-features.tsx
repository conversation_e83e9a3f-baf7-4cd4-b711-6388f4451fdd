import { useState } from "react"
import { ChevronDown, ChevronUp, Check, X } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import useSubscription from "./use-subscription"
import { PackageFeatureDetailType } from "@/core/domain/subscription/subscription"

export default function CollapsibleFeatures({ features }: { features: PackageFeatureDetailType }) {
  const { packageFeatureLabel } = useSubscription(null)
  const [isOpen, setIsOpen] = useState(false)

  return <div className="mt-4">
    <Button variant="outline" onClick={() => setIsOpen(!isOpen)} className="w-full justify-between">
      {isOpen ? "Hide Features" : "Show Features"}
      {isOpen ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
    </Button>
    {isOpen && (
      <div className="mt-4 space-y-2">
        {packageFeatureLabel.map((item) => (
          <div key={item.id} className="flex flex-col items-start justify-start">
            <span className="text-sm text-seekers-text-light">{item.label}</span>
            {typeof features[item.id] === "boolean" ? (
              features[item.id] ?
                <Check className="h-4 w-4 text-[#C19B67]" />
                :
                <X className="h-4 w-4 text-red-500" />

            ) : (
              <span className="text-sm max-sm:text-right font-medium">{features[item.id]}</span>
            )}
          </div>
        ))}
      </div>
    )}
  </div>
}
