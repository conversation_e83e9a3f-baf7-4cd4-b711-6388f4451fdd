import { useTranslations } from "next-intl";
import FilterContentLayout from "./filter-content-layout";
import { BaseSelectInputValue } from "@/types/base";
import { Droplet, Trash2, Wifi } from "lucide-react";
import CheckboxFilterItem from "./checkbox-filter-item";
import { useSeekerFilterStore } from "@/stores/seekers-filter-2.store";
import { FaBroom } from "react-icons/fa";

export default function RentalIncludingFilter() {
  const t = useTranslations("seeker")
  const { rentalIncluding, setRentalIncluding } = useSeekerFilterStore(state => state)
  const views: BaseSelectInputValue<string>[] = [
    {
      id: "1",
      content: <div className="flex gap-1 items-center">
        <Wifi className="w-4 h-4" strokeWidth={1.5} />
        <span className="">{t('listing.rentalIncludeFilter.optionOne.title')}</span>
      </div>,
      value: "wifi"
    },
    {
      id: "2",
      content: <div className="flex gap-1 items-center">
        <Trash2 className="w-4 h-4" strokeWidth={1.5} />
        <span className="">{t('listing.rentalIncludeFilter.optionTwo.title')}</span>
      </div>,
      value: "garbage"
    },
    {
      id: "3",
      content: <div className="flex gap-1 items-center">
        <Droplet className="w-4 h-4" strokeWidth={1} />
        <span className="">{t('listing.rentalIncludeFilter.optionThreetitle')}</span>
      </div>,
      value: "water"
    },
    {
      id: "4",
      content: <div className="flex gap-1 items-center">
        <FaBroom className="w-4 h-4" strokeWidth={1} />
        <span className="">{t('listing.rentalIncludeFilter.optionFour.title')}</span>
      </div>,
      value: "cleaning"
    },
  ]
  return <FilterContentLayout title={t('listing.rentalIncludeFilter.title')}>
    <div className="flex flex-wrap gap-2">
      {
        views.map(item =>
          <CheckboxFilterItem
            key={item.id}
            item={item}
            setValue={setRentalIncluding}
            isActive={rentalIncluding.includes(item.value)}
            className="" />
        )}
    </div>
  </FilterContentLayout>
}