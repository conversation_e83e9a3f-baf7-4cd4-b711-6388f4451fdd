import { useSeekerFilterStore } from "@/stores/seekers-filter-2.store"
import FilterContentLayout from "./filter-content-layout"
import { BaseSelectInputValue } from "@/types/base"
import { cn } from "@/lib/utils"
import CheckboxFilterItem from "./checkbox-filter-item"
import { useTranslations } from "next-intl"

export default function Electricity() {
  const t = useTranslations("seeker")
  const { electricity, setElectricity } = useSeekerFilterStore(state => state)
  const contractDruration: BaseSelectInputValue<string>[] = [
    {
      id: "1",
      content: t('listing.filter.elictricity.optionOne.title'),
      value: ""
    },
    {
      id: "2",
      content: t('listing.filter.elictricity.optionTwo.title'),
      value: "LOWER_THAN_5"
    },
    {
      id: "3",
      content: t('listing.filter.elictricity.optionThree.title'),
      value: "BETWEEN_5_10"
    },
    {
      id: "4",
      content: t('listing.filter.elictricity.optionFour.title'),
      value: "BETWEEN_10_20"
    },
    {
      id: "5",
      content: t('listing.filter.elictricity.optionFive.title'),
      value: "GREATER_THAN_20"
    },

  ]
  return <FilterContentLayout
    title={t('listing.filter.others.elictricity.title')}
    titleClassName="text-sm font-medium"
    className="space-y-3 !mt-3 w-full"
  >
    <div className="flex gap-2 max-sm:!flex-wrap">
      {contractDruration.map(item => <CheckboxFilterItem
        key={item.id}
        item={item}
        setValue={setElectricity}
        isActive={electricity == item.value}
        className={cn(
          "md:!w-full text-center items-center justify-center",
        )}
      />
      )}
    </div>
  </FilterContentLayout>
}