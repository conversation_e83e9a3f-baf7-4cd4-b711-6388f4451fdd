"use client"
import { useTranslations } from "next-intl";
import FilterContentLayout from "./filter-content-layout";
import SelectFilter from "./select-filter";
import { useSeekerFilterStore } from "@/stores/seeker-filter.store";

export default function OtherFilter() {
  const { typeContract, setTypeContract, minimumContract, setMinimumContract, yearsOfBuild, setYearsOfBuild } = useSeekerFilterStore(state => state)
  const t = useTranslations("seeker")
  return <FilterContentLayout title={t('listing.filter.others.title')}>
    <div className="grid md:grid-cols-2 gap-2">
      <SelectFilter
        title={t('listing.filter.others.typeContract.title')}
        value={typeContract}
        setValue={setTypeContract}
        placeholder=""
        options={[]}
      />
      <SelectFilter
        title={t('listing.filter.others.minimumContract.title')}
        value={typeContract}
        setValue={setTypeContract}
        placeholder=""
        options={[]}
      />
      <SelectFilter
        title={t('listing.filter.others.yearsOfBuild.title')}
        value={typeContract}
        setValue={setTypeContract}
        placeholder=""
        options={[]}
      />
    </div>
  </FilterContentLayout>
}