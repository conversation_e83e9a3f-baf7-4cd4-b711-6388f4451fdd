"use client"
import Dialog<PERSON><PERSON>er<PERSON>rapper from "@/components/dialog-wrapper/dialog-footer.wrapper"
import DialogHeaderWrapper from "@/components/dialog-wrapper/dialog-header-wrapper"
import DialogTitleWrapper from "@/components/dialog-wrapper/dialog-title-wrapper"
import DialogWrapper from "@/components/dialog-wrapper/dialog-wrapper"
import { Button } from "@/components/ui/button"
import { DialogDescription } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useTranslations } from "next-intl"
import phone from "phone"
import { useEffect, useState } from "react"

export default function ChangeContact({
  currentVal,
  onChange,
  trigger,
  type
}: {
  trigger: React.ReactNode,
  type: "email" | "phone",
  onChange: (val: string) => void,
  currentVal: string
}) {
  const t = useTranslations("seeker")
  const [newValue, setNewValue] = useState(currentVal)
  const [open, setOpen] = useState(false)
  const [error, setError] = useState<string | null>(null)
  useEffect(() => {
    setNewValue(currentVal)
  }, [currentVal])
  const validateEmail = (email: string) => {
    const re = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
    return re.test(String(email).toLowerCase())
  }

  const handleConfirm = () => {
    if (type === "email") {
      if (!validateEmail(newValue)) {
        setError(t("form.utility.invalidFormat", { field: t("form.label.email") }))
        return
      }
    } else if (type === "phone") {
      if (!phone(newValue).isValid) {
        setError(t("form.utility.invalidFormat", { field: t("form.label.phoneNumber") }))

        return
      }
    }
    onChange(newValue)
    setOpen(false)
  }
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewValue(e.target.value)
    setError(null)
  }


  return <DialogWrapper
    open={open}
    openTrigger={trigger}
    setOpen={setOpen}
  >
    <DialogHeaderWrapper>
      <DialogTitleWrapper className="text-base font-bold text-seekers-text">{t(`settings.personalInfo.change${type.charAt(0).toUpperCase() + type.slice(1)}.title`)}</DialogTitleWrapper>
      <DialogDescription className="max-w-sm">
        {t(`settings.personalInfo.change${type.charAt(0).toUpperCase() + type.slice(1)}.description`)}
      </DialogDescription>
    </DialogHeaderWrapper>
    <div className=" py-4 w-full">
      <div className="items-center gap-4">
        <Input
          id="new-value"
          type={type === "email" ? "email" : "tel"}
          className="col-span-3 w-full border border-input"
          value={newValue}
          onChange={(e) => handleChange(e)}
        />
      </div>
      {error && <p className="text-red-500 text-sm">{error}</p>}
    </div>
    <DialogFooterWrapper>
      <Button type="submit" variant={"default-seekers"} onClick={handleConfirm}>
        {t("cta.confirm")}
      </Button>
    </DialogFooterWrapper>
  </DialogWrapper>
}