"use client"
import { useTranslations } from "next-intl";
import FilterContentLayout from "./filter-content-layout";
import SelectFilter from "./select-filter";
import { useSeekerFilterStore } from "@/stores/seekers-filter-2.store";
import Elictricity from "./electricity";
import { BaseSelectInputValue } from "@/types/base";
import { useGetFilterParameter } from "@/core/applications/queries/listing/use-get-filter-parameters";
import { useEffect, useState } from "react";

export default function OtherFeatureFilter() {
  const { typeLiving, setTypeLiving, parkingStatus, setParkingStatus, poolStatus, setPoolStatus, furnishedStatus, setFurnishedStatus } = useSeekerFilterStore(state => state)
  const t = useTranslations("seeker")
  const filterParameterQuery = useGetFilterParameter()
  const [parkingOptions, setParkingOptions] = useState<BaseSelectInputValue<string>[]>([])
  const [poolOptions, setPoolOptions] = useState<BaseSelectInputValue<string>[]>([])
  const [typeLivingOptions, setTypeLivingOptions] = useState<BaseSelectInputValue<string>[]>([])
  const [furnishingOptions, setFurnishingOptions] = useState<BaseSelectInputValue<string>[]>([])
  useEffect(() => {
    if (filterParameterQuery.isPending) return
    const parkingOptionsQuery = filterParameterQuery.data?.data?.parkingOptions
    const poolOptionsQuery = filterParameterQuery.data?.data?.poolOptions
    const typeLivingOptionsQuery = filterParameterQuery.data?.data?.livingOptions
    const furnishingOptionsQuery = filterParameterQuery.data?.data?.furnishingOptions
    if (parkingOptionsQuery) {
      const parkingSelectOptions: BaseSelectInputValue<string>[] = parkingOptionsQuery.map((item, idx) => ({
        id: idx.toString(),
        content: item.title,
        value: item.value
      }))
      setParkingOptions(parkingSelectOptions)
    }

    if (poolOptionsQuery) {
      const poolSelectOptions: BaseSelectInputValue<string>[] = poolOptionsQuery.map((item, idx) => ({
        id: idx.toString(),
        content: item.title,
        value: item.value
      }))
      setPoolOptions(poolSelectOptions)
    }

    if (typeLivingOptionsQuery) {
      const livingSelectOptions: BaseSelectInputValue<string>[] = typeLivingOptionsQuery.map((item, idx) => ({
        id: idx.toString(),
        content: item.title,
        value: item.value
      }))
      setTypeLivingOptions(livingSelectOptions)
    }
    if (furnishingOptionsQuery) {
      const furnishingSelectOptions: BaseSelectInputValue<string>[] = furnishingOptionsQuery.map((item, idx) => ({
        id: idx.toString(),
        content: item.title,
        value: item.value
      }))
      setFurnishingOptions(furnishingSelectOptions)
    }
  }, [filterParameterQuery.data?.data?.furnishingOptions,
  filterParameterQuery.data?.data?.livingOptions,
  filterParameterQuery.data?.data?.parkingOptions,
  filterParameterQuery.data?.data?.poolOptions,
  filterParameterQuery.isPending])
  const anyOption: BaseSelectInputValue<string> = {
    id: "67",
    content: t('misc.any'),
    value: "ANY"
  }
  return <FilterContentLayout title={t('listing.filter.othersFeature.title')}>
    <Elictricity />
    <div className="grid md:grid-cols-2 gap-2">
      <SelectFilter
        title={t('listing.filter.others.parking.title')}
        value={parkingStatus}
        setValue={setParkingStatus}
        placeholder=""
        options={[anyOption, ...parkingOptions]}
      />
      <SelectFilter
        title={t('listing.filter.others.pool.title')}
        value={poolStatus}
        setValue={setPoolStatus}
        placeholder=""
        options={[anyOption, ...poolOptions]}
      />
      <SelectFilter
        title={t('listing.filter.others.closeOrOpenLiving.title')}
        value={typeLiving}
        setValue={setTypeLiving}
        placeholder=""
        options={[anyOption, ...typeLivingOptions]}
      />
      <SelectFilter
        title={t('listing.filter.others.furnished.title')}
        value={furnishedStatus}
        setValue={setFurnishedStatus}
        placeholder=""
        options={[anyOption, ...furnishingOptions]}
      />
    </div>
  </FilterContentLayout>
}