import moment from "moment"
import { useTranslations } from "next-intl"

export default function AvailableAt({ availableAt }: { availableAt: string | null }) {
  const t = useTranslations("seeker")
  if (availableAt == null) return <></>
  const isInFuture = moment().isBefore(moment(availableAt))
  if (!isInFuture) return <></>
  return <p className="text-seekers-text-light">{t("listing.misc.availabelAt")} {moment(availableAt).format("DD MMM YYYY")}</p>
}