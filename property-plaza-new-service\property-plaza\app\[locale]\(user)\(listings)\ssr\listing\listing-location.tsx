import { ListingListSeekers } from "@/core/domain/listing/listing-seekers"
import { cn } from "@/lib/utils"
import { MapPin } from "lucide-react"
import ListingWrapper from "./listing-wrapper"

interface ListingTitleProps {
  listing: ListingListSeekers,
  className?: string
}
export function ListingLocation({ className, listing }: ListingTitleProps) {
  return <ListingWrapper
    listing={listing}
    className={cn("flex items-center text-xs gap-1 text-seekers-text-light font-medium", className)}>

    <MapPin className="w-4 h-4" />
    <p>
      {listing.location}
    </p>
  </ListingWrapper>
}