import { ListingContractType } from "@/core/domain/listing/listing"
import { ItemWithSuffix } from "@/core/domain/utils/utils"
import { seekersPriceHelper } from "@/lib/listing-price-formatter"
import FormatPrice from "./format-price"
import TooltipWrapper from "@/components/tooltop-wrapper/tooltip-wrapper"
import { Info } from "lucide-react"
import { getTranslations } from "next-intl/server"
import { getCurrencyConversion } from "@/core/infrastructures/currency-converter/api"

export default async function PriceInfo({ price, type, minDuration, maxDuration, isNegotiable, currency = "EUR", locale = "EN" }: {
  price: number,
  type: ListingContractType
  minDuration: ItemWithSuffix,
  maxDuration: ItemWithSuffix,
  currency?: string,
  locale?: string,
  isNegotiable?: boolean
}) {
  const t = await getTranslations("seeker")
  const conversionRates = await getCurrencyConversion()
  const { startWord, formattedPrice, suffix } = await seekersPriceHelper(
    price,
    type,
    minDuration,
    maxDuration
  )
  return <div>
    <p className="max-md:text-xs font-medium text-seekers-text-lighter">
      {startWord}
    </p>

    <div className="max-md:flex max-md:items-center max-md:flex-wrap relative">
      <FormatPrice price={formattedPrice} currency_={currency} locale_={locale} conversions={conversionRates.data} />
      <p className="max-md:text-[10px] md:text-end text-xs capitalize">{suffix}</p>
      {
        isNegotiable &&
        <div className="md:absolute md:-right-2 md:top-0">
          <TooltipWrapper
            trigger={<Info className="w-3 h-3 -mt-2 ml-1 text-seekers-primary" />}
            content={<>
              <p className="text-seekers-primary max-w-xs text-xs">{t('misc.priceIsNegotiable')}</p>
            </>}
          />
        </div>
      }
    </div>
  </div>
}