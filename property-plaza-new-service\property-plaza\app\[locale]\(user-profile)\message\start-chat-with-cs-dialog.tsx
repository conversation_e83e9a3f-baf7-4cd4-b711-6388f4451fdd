import DialogWrapper from "@/components/dialog-wrapper/dialog-wrapper";
import { But<PERSON> } from "@/components/ui/button";
import { DialogTitle } from "@/components/ui/dialog";
import { useTranslations } from "next-intl";
import { useState } from "react";
import ChatWithCSForm from "./form/chat-with-cs.form";
import DialogHeaderWrapper from "@/components/dialog-wrapper/dialog-header-wrapper";

export default function StartChatWithCS({ customTrigger }: { customTrigger?: React.ReactNode }) {
  const t = useTranslations("seeker")

  const [open, setOpen] = useState(false)
  return <DialogWrapper
    open={open}
    setOpen={setOpen}
    openTrigger={customTrigger ||
      <Button className="w-full border-seekers-primary text-seekers-textz hover:bg-seekers-primary/30" size={"sm"} variant={"outline"}>
        {t('cta.chatCustomerService')}
      </Button>}
  >
    <DialogHeaderWrapper className="text-start px-0">
      <DialogTitle className="font-semibold">{t('message.chatCs.title')}</DialogTitle>
    </DialogHeaderWrapper>
    <div className="space-y-2">
      <p>{t('message.chatCs.description')}</p>
      <ChatWithCSForm submitHandler={() => setOpen(false)} />
    </div>
  </DialogWrapper>
} 