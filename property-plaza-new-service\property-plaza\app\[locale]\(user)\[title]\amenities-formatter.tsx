import PlumbingIcon from "@/components/icons/property-detail/Plumbing.svg"
import GazeboIcon from "@/components/icons/property-detail/Gazebo.svg"
import ConstructionNearbyIcon from "@/components/icons/property-detail/Construction nearby-next to the location.svg"
import PetAllowedIcon from "@/components/icons/property-detail/Pet allowed.svg"
import SubleasedAllowedIcon from "@/components/icons/property-detail/Sublease allowed.svg"
import RecentlyRenovatedIcon from "@/components/icons/property-detail/Recently renovated.svg"
import RooftopTerraceIcon from "@/components/icons/property-detail/Rooftop terrace.svg"
import BathubIcon from "@/components/icons/property-detail/Bathtub.svg"
import TerraceIcon from "@/components/icons/property-detail/Terrace.svg"
import AirConditionIcon from "@/components/icons/property-detail/Air Conditioning.svg"
import BalconyIcon from "@/components/icons/property-detail/Balcony.svg"
import MunicipalWaterwork from "@/components/icons/property-detail/Municipal Waterworks.svg"
import { useTranslations } from "next-intl"
import Image from "next/image"
import { Star } from "lucide-react"
import { cn } from "@/lib/utils"

export default function AmenitiesFormatter({ amenities, className, showText = true }: { amenities: string, className?: string, showText?: boolean }) {
  const t = useTranslations("seeker")
  switch (amenities) {
    case "PLUMBING":
      return <div className="flex gap-2 text-sm">
        <Image src={PlumbingIcon || ""}
          alt={t('listing.propertyCondition.optionFour.title')}
          aria-label={t('listing.feature.additionalFeature.plumbing')}
          className={cn("w-6 h-6", className)}
          width={24}
          height={24}
        />
        {showText && t('listing.propertyCondition.optionFour.title')}
      </div>
    case "GAZEBO":
      return <div className="flex gap-2 text-sm">
        <Image
          src={GazeboIcon || ""}
          alt={t('listing.feature.additionalFeature.gazebo')}
          aria-label={t('listing.feature.additionalFeature.gazebo')}
          className={cn("w-6 h-6", className)}
          width={24}
          height={24}
        />
        {showText && t('listing.feature.additionalFeature.gazebo')}
      </div>
    case "CONSTRUCTION_NEARBY":
      return <div className="flex gap-2 text-sm">
        <Image
          src={ConstructionNearbyIcon || ""}
          alt={t('listing.feature.additionalFeature.constructionNearby')}
          aria-label={t('listing.feature.additionalFeature.constructionNearby')}
          className={cn("w-6 h-6", className)}
          width={24}
          height={24}
        />
        {showText && t('listing.feature.additionalFeature.constructionNearby')}
      </div>
    case "PET_ALLOWED":
      return <div className="flex gap-2 text-sm">
        <Image
          src={PetAllowedIcon || ""}
          alt={t('listing.feature.additionalFeature.petAllowed')}
          aria-label={t('listing.feature.additionalFeature.petAllowed')}
          className={cn("w-6 h-6", className)}
          width={24}
          height={24}
        />
        {showText && t('listing.feature.additionalFeature.petAllowed')}
      </div>
    case "SUBLEASE_ALLOWED":
      return <div className="flex gap-2 text-sm">
        <Image src={SubleasedAllowedIcon || ""}
          aria-label={t('listing.feature.additionalFeature.subleaseAllowed')}
          alt={t('listing.feature.additionalFeature.subleaseAllowed')}
          className={cn("w-6 h-6", className)}
          width={24}
          height={24}
        />
        {showText && t('listing.feature.additionalFeature.subleaseAllowed')}
      </div>
    case "RECENTLY_RENOVATED":
      return <div className="flex gap-2 text-sm">
        <Image src={RecentlyRenovatedIcon || ""}
          alt={t('listing.feature.additionalFeature.recentlyRenovated')}
          aria-label={t('listing.feature.additionalFeature.recentlyRenovated')}
          className={cn("w-6 h-6", className)} width={24}
          height={24}
        />
        {showText && t('listing.feature.additionalFeature.recentlyRenovated')}
      </div>
    case "ROOFTOP_TERRACE":
      return <div className="flex gap-2 text-sm">
        <Image
          src={RooftopTerraceIcon || ""} alt={t('listing.feature.additionalFeature.rooftopTerrace')} aria-label={t('listing.feature.additionalFeature.rooftopTerrace')} className={cn("w-6 h-6", className)} width={24}
          height={24} />
        {showText && t('listing.feature.additionalFeature.rooftopTerrace')}
      </div>
    case "GARDEN_BACKYARD":
      return <div className="flex gap-2 text-sm">
        <Image src={RooftopTerraceIcon || ""}
          aria-label={t('listing.feature.additionalFeature.garden')}
          alt={t('listing.feature.additionalFeature.garden')}
          className={cn("w-6 h-6", className)}
          width={24}
          height={24}
        />
        {showText && t('listing.feature.additionalFeature.garden')}
      </div>
    case "BATHUB":
      return <div className="flex gap-2 text-sm">
        <Image src={BathubIcon || ""} alt={t('listing.feature.additionalFeature.bathub')}
          aria-label={t('listing.feature.additionalFeature.bathub')}
          className={cn("w-6 h-6", className)}
          width={24}
          height={24}
        />
        {showText && t('listing.feature.additionalFeature.bathub')}
      </div>

    case "TERRACE":
      return <div className="flex gap-2 text-sm">
        <Image src={TerraceIcon || ""} alt={t('listing.feature.additionalFeature.terrace')}
          aria-label={t('listing.feature.additionalFeature.terrace')}
          className={cn("w-6 h-6", className)}
          width={24}
          height={24}
        />
        {showText && t('listing.feature.additionalFeature.terrace')}
      </div>
    case "AIR_CONDITION":
      return <div className="flex gap-2 text-sm">
        <Image src={AirConditionIcon || ""} aria-label={t('listing.feature.additionalFeature.airCondition')}
          alt={t('listing.feature.additionalFeature.airCondition')} className={cn("w-6 h-6", className)}
          width={24}
          height={24}
        />
        {showText && t('listing.feature.additionalFeature.airCondition')}
      </div>
    case "BALCONY":
      return <div className="flex gap-2 text-sm">
        <Image
          src={BalconyIcon || ""}
          alt={t('listing.feature.additionalFeature.balcony')}
          aria-label={t('listing.feature.additionalFeature.balcony')}
          className={cn("w-6 h-6", className)}
          width={24}
          height={24}
        />
        {showText && t('listing.feature.additionalFeature.balcony')}
      </div>
    case "MUNICIPAL_WATERWORK":
      return <div className="flex gap-2 text-sm">
        <Image
          src={MunicipalWaterwork || ""}
          alt={t('listing.feature.additionalFeature.municipalWaterwork')}
          aria-label={t('listing.feature.additionalFeature.municipalWaterwork')}
          className={cn("w-6 h-6", className)}
          width={24}
          height={24}
        />
        {showText && t('listing.feature.additionalFeature.municipalWaterwork')}
      </div>
    default:
      return <div className="flex gap-2 text-sm">
        <Star />
        {amenities}
      </div>
  }
}