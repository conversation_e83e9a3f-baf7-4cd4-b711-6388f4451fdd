"use client"
import { useState } from "react";
import VerifyHero from "./components/verify-hero";
import VerifyHowItWorks from "./components/verify-how-it-works";
import VerifyPricing from "./components/verify-pricing";
import VerifyBookingForm from "./components/verify-booking-form";

interface PricingTier {
  id: string;
  name: string;
  price: number;
  popular?: boolean;
  features: string[];
}

interface VerifyPageClientProps {
  conversions: { [key: string]: number };
}

export default function VerifyPageClient({ conversions }: VerifyPageClientProps) {
  const [selectedTier, setSelectedTier] = useState<PricingTier | undefined>();

  const handleSelectTier = (tier: PricingTier) => {
    setSelectedTier(tier);
    // Scroll to booking form
    setTimeout(() => {
      document.getElementById('booking-form')?.scrollIntoView({ 
        behavior: 'smooth',
        block: 'start'
      });
    }, 100);
  };

  return (
    <main className="min-h-screen">
      <VerifyHero />
      <VerifyHowItWorks />
      <VerifyPricing
        conversions={conversions}
        onSelectTier={handleSelectTier}
      />
      <VerifyBookingForm
        selectedTier={selectedTier}
        conversions={conversions}
      />
    </main>
  );
}
