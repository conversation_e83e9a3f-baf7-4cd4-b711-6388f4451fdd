import { Metadata } from "next"
import { getLocale, getTranslations } from "next-intl/server"
import NotificationBreadCrumb from "./bread-crumb"
import MainContentLayout from "@/components/seekers-content-layout/main-content-layout"
import { useTranslations } from "next-intl"
import NotificationForm from "./form/notification.form"
import { notificationSettingUrl } from "@/lib/constanta/route"
import { routing } from "@/lib/locale/routing"

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations("seeker")
  const locale = await getLocale() || routing.defaultLocale
  const baseUrl = process.env.USER_DOMAIN || "https://www.property-plaza.com/"
  return {
    title: t("metadata.notificationSetting.title"),
    description: t("metadata.notificationSetting.description"),
    alternates: {
      canonical: baseUrl + locale + "",
      languages: {
        en: baseUrl + "en" + notificationSettingUrl,
        id: baseUrl + "id" + notificationSettingUrl,
        "x-default": baseUrl + notificationSettingUrl.replace("/", ""),
      }
    },
    openGraph: {
      title: t('metadata.rootLayout.title'),
      description: t('metadata.rootLayout.description'),
      images: [
        {
          url: baseUrl + "og.png",
          width: 1200,
          height: 630,
          alt: "Property Plaza",
        }
      ],
      type: "website",
      url: baseUrl + locale,
      countryName: "Indonesia",
      emails: "<EMAIL>",
      locale: locale,
      alternateLocale: routing.locales,
      siteName: "Property plaza"
    },
    applicationName: "Property plaza",
    twitter: {
      card: "summary_large_image",
      title: t('metadata.rootLayout.title'),
      description: t('metadata.rootLayout.description'),
      images: [baseUrl + "og.jpg"],
    },

    robots: {
      index: false,
      follow: false,
      nocache: false,
    },
  }
}



export default function NotificationPage() {
  const t = useTranslations("seeker")

  return <>
    <NotificationBreadCrumb />
    <MainContentLayout className="space-y-8 my-8 max-sm:px-0">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">{t("setting.profile.notification.title")}</h1>
        <h2 className="text-muted-foreground mt-2">{t("settings.profile.notification.description")}</h2>
      </div>
      <NotificationForm />
    </MainContentLayout>

  </>
}