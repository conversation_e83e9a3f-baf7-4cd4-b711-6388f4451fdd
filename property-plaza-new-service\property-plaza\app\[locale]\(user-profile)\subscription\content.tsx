"use client"
import { useTranslations } from "next-intl";
import useSubscription from "./use-subscription";
import React, { useState } from "react";
import SegmentControl from "./segment-control";
import { cn } from "@/lib/utils";
import PackageContent from "./subscription-package-detail";
import PackageWrapper from "./subscription-package-container";
import { useUserStore } from "@/stores/user.store";
import { packages, PackagesType, Subscription } from "@/core/domain/subscription/subscription";
import { useSubscribePlan } from "@/core/applications/mutations/subscription/use-subscribe-plan";
import { useToast } from "@/hooks/use-toast";
import { useUpdateSubscribePlan } from "@/core/applications/mutations/subscription/use-update-plan";
import { useCancelSubscribePlan } from "@/core/applications/mutations/subscription/use-cancel-subscription-plan";
import { useGetTransactionSeekerQuery } from "@/core/applications/queries/transaction/use-get-transaction-seeker-list";
import Cookies from "js-cookie";
import { ACCESS_TOKEN } from "@/lib/constanta/constant";



export default function SubscriptionContent({ conversionRate, SubscriptionPackages }: { conversionRate: { [key: string]: string }, SubscriptionPackages: Subscription[] }) {
  const t = useTranslations("seeker")
  const { seekers } = useUserStore()
  const { toast } = useToast()
  const accessToken = Cookies.get(ACCESS_TOKEN)
  const subscribePlanMutation = useSubscribePlan()
  const upgradeSubscriptionMutation = useUpdateSubscribePlan()
  const cancelSubscriptionMutation = useCancelSubscribePlan()
  const [billingInterval, setBillingInterval] = useState<"monthly" | "quarterly">("monthly")
  const { availablePlan, handleSetPackage, packageFeatureLabel, handleDowngradeLevelLabel, handleUpgradeLevelLabel } = useSubscription(SubscriptionPackages)
  const membership = useUserStore(state => state.seekers.accounts.membership)
  const transactionQuery = useGetTransactionSeekerQuery({
    page: 1,
    per_page: 1,
    search: "",
    type: "",
    start_date: "",
    end_date: ""
  }, accessToken ? true : false)
  const handleUpgrade = async (productId: string, priceId: string) => {
    try {
      if (seekers.accounts.membership === packages.free) {
        const response = await subscribePlanMutation.mutateAsync({
          price_id: priceId,
          product_id: productId,
        })
        window.open(response.data.data.url, "_blank")
      } else {
        const response = await upgradeSubscriptionMutation.mutateAsync({
          price_id: priceId,
          product_id: productId,
        })
        toast({
          title: t("success.upgradeSubscription")
        })
        // window.location.reload()
      }
    } catch (e: any) {
      toast({
        title: t('error.Subscribing'),
        description: e.response.data.message || "",
        variant: "destructive"
      })
    }


  }
  const handleDowngrade = async (productId: string, priceId: string) => {
    try {
      if (seekers.accounts.membership === packages.finder) {
        const response = await cancelSubscriptionMutation.mutateAsync()

      }
      else {
        const response = await upgradeSubscriptionMutation.mutateAsync({
          price_id: priceId,
          product_id: productId,
        })
        toast({
          title: t("success.downGrade")
        })
        window.location.reload()
      }
    } catch (e: any) {
      toast({
        title: t('error.Subscribing'),
        description: e.response.data.message || "",
        variant: "destructive"
      })
    }
  }
  return <div className="mt-8 mb-12 w-full space-y-8 ">
    <div className="flex justify-center">
      <SegmentControl
        value={billingInterval}
        onValueChange={(value) => setBillingInterval(value as "monthly" | "quarterly")}
        options={[
          { value: "monthly", label: t("setting.subscriptionStatus.subscription.monthly") },
          { value: "quarterly", label: t("setting.subscriptionStatus.subscription.quarterly"), badge: "-15%" },
        ]}
      />
    </div>
    <section className={cn("grid gap-4", availablePlan.length < 3 ? "md:grid-cols-3" : "md:grid-cols-4")}>
      <div className="max-sm:hidden">
        <div className="h-[184px]"></div> {/* Spacer to align with plan headers */}
        {packageFeatureLabel.map((item, idx) => <div
          key={item.id}
          className={cn(
            idx == 0 ? "" : "border-t border-dashed border-gray-200",
            "h-12 flex items-center mx-4"
          )}
        >
          {item.label}
        </div>)}
      </div>
      {availablePlan.map(item => <PackageWrapper
        key={item.productId}
        isMostPopular={item.name == packages.archiver}
      >
        <PackageContent
          plan={item}
          isQuaterlyBilling={billingInterval === "quarterly"}
          conversionRate={conversionRate}
          features={handleSetPackage(item.name as PackagesType)}
          isCurrentPlan={membership == item.name}
          canDowngrade={handleDowngradeLevelLabel(membership as PackagesType) !== ""}
          canUpgrade={handleUpgradeLevelLabel(item.name as PackagesType) !== "" && membership !== packages.archiver}
          onDowngrade={(productId: string, priceId: string) => handleDowngrade(productId, priceId)}
          onUpgrde={(productId: string, priceId: string) => handleUpgrade(productId, priceId)}
          isLoading={subscribePlanMutation.isPending || upgradeSubscriptionMutation.isPending || cancelSubscriptionMutation.isPending}
          nextBillingDate={transactionQuery.data?.data?.data[0]?.nextBilling || ""}
        />
      </PackageWrapper>)}
    </section>
  </div>
}

