import { <PERSON>, CardHeader } from "../ui/card";
import { motion, AnimatePresence } from 'framer-motion';

export function SearchCardWrapper({ children, setActive, id }: { children: React.ReactNode, setActive: (val: string) => void, id: string }) {
  return <Card className="shadow-none rounded-md" onClick={() => setActive(id)}>
    {children}
  </Card>
}

export function SearchCardHeaderWrapper({ children }: { children: React.ReactNode }) {
  return <CardHeader className="flex flex-row justify-between items-center p-4">
    {children}
  </CardHeader>
}

export function SearchCardContentWrapper({ children, isActive = false, }: { children: React.ReactNode, isActive?: boolean, }) {
  return <AnimatePresence>
    {isActive && (
      <motion.div
        className="overflow-hidden p-4"
        initial={{ height: 0, padding: 0 }}
        animate={{ height: "auto", padding: "0 1rem 0" }}
        exit={{ height: 0, padding: 0 }}
        transition={{ duration: 0.3, ease: 'easeInOut' }}
      >
        <div>
          {children}
        </div>
      </motion.div>
    )
    }
  </AnimatePresence >

}