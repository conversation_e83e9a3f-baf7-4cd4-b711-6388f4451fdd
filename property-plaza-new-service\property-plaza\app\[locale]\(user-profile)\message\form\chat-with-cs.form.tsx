import { useTranslations } from "next-intl"
import useChatWithCsFormSchema from "./chat-with-cs-form.schema"
import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { Form } from "@/components/ui/form"
import TextAreaInput from "@/components/input-form/text-area-input"
import { Button } from "@/components/ui/button"
import { usePostNewChat } from "@/core/applications/mutations/messages/use-post-new-chat"
import { NewChatDto } from "@/core/infrastructures/messages/dto"
import { useToast } from "@/hooks/use-toast"
import { useMessagingStore } from "@/stores/messaging.store"
import { MIN_MESSAGE_COUNT } from "@/lib/constanta/constant"
import { useQueryClient } from "@tanstack/react-query"
import { CHAT_LIST } from "@/core/applications/queries/messages/use-get-chat-list"

export default function ChatWithCSForm({ submitHandler }: { submitHandler: () => void }) {
  const t = useTranslations("seeker")
  const queryClient = useQueryClient()
  const formSchema = useChatWithCsFormSchema()
  type formSchemaType = z.infer<typeof formSchema>
  const postNewChat = usePostNewChat()
  const form = useForm<formSchemaType>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      text: ""
    }
  })
  const { toast } = useToast()
  async function onSubmit(values: formSchemaType) {
    if (values.text.trim().length < MIN_MESSAGE_COUNT) {
      toast({
        title: t('error.messageTooShort.title'),
        description: t('error.messageTooShort.description'),
        variant: "destructive"
      })
      return
    }
    const data: NewChatDto = {
      category: "CUSTOMER_SUPPORT",
      requested_by: "CLIENT",
      message: values.text
    }
    try {
      await postNewChat.mutateAsync(data)
      queryClient.invalidateQueries({ queryKey: [CHAT_LIST] })
      toast({
        title: t('success.sendMessageToCs.title'),
        description: t('success.sendMessageToCs.description')
      })
      submitHandler()
    } catch (error: any) {
      toast({
        title: t("error.failedSendMessage.title"),
        description: error.response?.data.message || "",
        variant: "destructive"
      })
    }
  }
  return <div className="w-full space-y-2">

    <Form {...form} >
      <form onSubmit={form.handleSubmit(onSubmit)} className="z-50">

        <TextAreaInput
          form={form}
          label=""
          name="text"
          placeholder={t('form.placeholder.example.requestHelpToCs')}
        />
      </form>
      <Button variant={"default-seekers"} loading={postNewChat.isPending} onClick={() => onSubmit(form.getValues())} className="min-w-40 max-sm:w-full">{t('cta.sendRequest')}</Button>
    </Form>
  </div>
}