name: Auto Assign Issues

on:
  issues:
    types: [opened]

permissions:
  issues: write
  repository-projects: write
  contents: read

jobs:
  assign:
    runs-on: ubuntu-latest

    steps:
      - name: Assign issue based on title
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const issue = context.payload.issue;
            const issueTitle = issue.title.toUpperCase();

            const assignments = {
                "BACKEND": "jowy2211",
                "BO": "jowy2211",
                "SEEKERS": "<PERSON><PERSON><PERSON>r",
                "SEEKER": "<PERSON>u<PERSON>r",
                "OWNER": "<PERSON>u<PERSON>r",
                "OWNERS": "<PERSON>uuper"
            };

            let assignedUser = null;
            for (const [keyword, user] of Object.entries(assignments)) {
                if (issueTitle.includes(`[${keyword}]`)) {
                    assignedUser = user;
                    break;
                }
            }

            if (assignedUser) {
                await github.rest.issues.addAssignees({
                    owner: context.repo.owner,
                    repo: context.repo.repo,
                    issue_number: issue.number,
                    assignees: [assignedUser]
                });
                console.log(`Issue #${issue.number} assigned to ${assignedUser}`);
            } else {
                console.log(`No assignment needed for issue #${issue.number}`);
            }

            // Fetch the project item ID
            const projectId = 'PVT_kwDOCumSAM4Aw9MI';
            const statusFieldId = 'PVTSSF_lADOCumSAM4Aw9MIzgnJOIk';
            const backlogOptionId = 'f75ad846';

            const projectItemsQuery = `
                query {
                    repository(owner: "${context.repo.owner}", name: "${context.repo.repo}") {
                        issue(number: ${issue.number}) {
                            projectItems(first: 10) {
                                nodes {
                                    id
                                }
                            }
                        }
                    }
                }
            `;

            const projectItemsResponse = await github.graphql(projectItemsQuery);
            const projectItemId = projectItemsResponse.repository.issue.projectItems.nodes[0]?.id;

            if (!projectItemId) {
                console.log(`No project item found for issue #${issue.number}`);
                return;
            }

            console.log(`Updating status for project item ID: ${projectItemId}`);

            const updateProjectMutation = `
                mutation {
                    updateProjectV2ItemFieldValue(input: {
                        projectId: "${projectId}"
                        itemId: "${projectItemId}"
                        fieldId: "${statusFieldId}"
                        value: {
                            singleSelectOptionId: "${backlogOptionId}"
                        }
                    }) {
                        projectV2Item {
                            id
                        }
                    }
                }
            `;

            await github.graphql(updateProjectMutation);
            console.log(`Status set to "Backlog" for issue #${issue.number}`);
