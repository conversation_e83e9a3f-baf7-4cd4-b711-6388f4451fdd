import TooltipWrapper from "@/components/tooltop-wrapper/tooltip-wrapper"
import { SellingPoint } from "@/core/domain/listing/listing-seekers"
import { useTranslations } from "next-intl"
import AmenitiesFormatter from "../[title]/amenities-formatter"

const SellingPointList = {
  plumbing: "PLUMBING",
  subleaseAllowed: "SUBLEASE_ALLOWED",
  balcony: "BALCONY",
  gazebo: "GAZEBO",
  recentlyRenovated: "RECENTLY_RENOVATED",
  airCondition: "AIR_CONDITION",
  constructionNearby: "CONSTRUCTION_NEARBY",
  rooftopTerrace: "ROOFTOP_TERRACE",
  terrace: "TERRACE",
  petAllowed: "PET_ALLOWED",
  garden: "GARDEN_BACKYARD",
  bathub: "BATHUB"
}
export default function SellingPointFormatter({ value }: SellingPoint) {
  const t = useTranslations("seeker")
  switch (value) {
    case SellingPointList.plumbing:
      return <TooltipWrapper
        trigger={
          <div className="cursor-pointer">
            <AmenitiesFormatter
              amenities={SellingPointList.plumbing}
              className="!w-4 !h-4"
              showText={false}
            />
          </div>
        }
        content={<p>{t('listing.feature.additionalFeature.plumbing')}</p>}
        contentClassName="text-seekers-primary p-2 text-sm"
      />
    case SellingPointList.airCondition:
      return <TooltipWrapper
        trigger={
          <div className="cursor-pointer">
            <AmenitiesFormatter amenities={SellingPointList.airCondition} className="!w-4 !h-4" showText={false} />
          </div>
        }
        content={<p>{t('listing.feature.additionalFeature.airCondition')}</p>}
        contentClassName="text-seekers-primary p-2 text-sm"
      />
    case SellingPointList.balcony:
      return <TooltipWrapper
        trigger={
          <div className="cursor-pointer">
            <AmenitiesFormatter amenities={SellingPointList.balcony} className="!w-4 !h-4" showText={false} />
          </div>
        }
        content={<p>{t('listing.feature.additionalFeature.balcony')}</p>}
        contentClassName="text-seekers-primary p-2 text-sm"
      />
    case SellingPointList.bathub:
      return <TooltipWrapper
        trigger={
          <div className="cursor-pointer">
            <AmenitiesFormatter amenities={SellingPointList.bathub} className="!w-4 !h-4" showText={false} />
          </div>
        }
        content={<p>{t('listing.feature.additionalFeature.bathub')}</p>}
        contentClassName="text-seekers-primary p-2 text-sm"
      />
    case SellingPointList.constructionNearby:
      return <TooltipWrapper
        trigger={
          <div className="cursor-pointer">
            <AmenitiesFormatter amenities={SellingPointList.constructionNearby} className="!w-4 !h-4" showText={false} />
          </div>
        }
        content={<p>{t('listing.feature.additionalFeature.constructionNearby')}</p>}
        contentClassName="text-seekers-primary p-2 text-sm"
      />
    case SellingPointList.garden:
      return <TooltipWrapper
        trigger={
          <div className="cursor-pointer">
            <AmenitiesFormatter amenities={SellingPointList.garden} className="!w-4 !h-4" showText={false} />
          </div>
        }
        content={<p>{t('listing.feature.additionalFeature.garden')}</p>}
        contentClassName="text-seekers-primary p-2 text-sm"
      />
    case SellingPointList.gazebo:
      return <TooltipWrapper
        trigger={
          <div className="cursor-pointer">
            <AmenitiesFormatter amenities={SellingPointList.gazebo} className="!w-4 !h-4" showText={false} />
          </div>
        }
        content={<p>{t('listing.feature.additionalFeature.gazebo')}</p>}
        contentClassName="text-seekers-primary p-2 text-sm"
      />
    case SellingPointList.petAllowed:
      return <TooltipWrapper
        trigger={
          <div className="cursor-pointer">
            <AmenitiesFormatter amenities={SellingPointList.petAllowed} className="!w-4 !h-4" showText={false} />
          </div>
        }
        content={<p>{t('listing.feature.additionalFeature.petAllowed')}</p>}
        contentClassName="text-seekers-primary p-2 text-sm"
      />
    case SellingPointList.recentlyRenovated:
      return <TooltipWrapper
        trigger={
          <div className="cursor-pointer">
            <AmenitiesFormatter amenities={SellingPointList.recentlyRenovated} className="!w-4 !h-4" showText={false} />
          </div>
        }
        content={<p>{t('listing.feature.additionalFeature.recentlyRenovated')}</p>}
        contentClassName="text-seekers-primary p-2 text-sm"
      />
    case SellingPointList.rooftopTerrace:
      return <TooltipWrapper
        trigger={
          <div className="cursor-pointer">
            <AmenitiesFormatter amenities={SellingPointList.rooftopTerrace} className="!w-4 !h-4" showText={false} />
          </div>
        }
        content={<p>{t('listing.feature.additionalFeature.rooftopTerrace')}</p>}
        contentClassName="text-seekers-primary p-2 text-sm"
      />
    case SellingPointList.subleaseAllowed:
      return <TooltipWrapper
        trigger={
          <div className="cursor-pointer">
            <AmenitiesFormatter amenities={SellingPointList.subleaseAllowed} className="!w-4 !h-4" showText={false} />
          </div>
        }
        content={<p>{t('listing.feature.additionalFeature.subleaseAllowed')}</p>}
        contentClassName="text-seekers-primary p-2 text-sm"
      />
    case SellingPointList.terrace:
      return <TooltipWrapper
        trigger={
          <div className="cursor-pointer">
            <AmenitiesFormatter amenities={SellingPointList.terrace} className="!w-4 !h-4" showText={false} />
          </div>
        }
        content={<p>{t('listing.feature.additionalFeature.terrace')}</p>}
        contentClassName="text-seekers-primary p-2 text-sm"
      />
    case "":
      return <></>
    default:
      return <></>
  }
}