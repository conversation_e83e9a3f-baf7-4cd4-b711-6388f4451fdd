"use client"

import { useMediaQuery } from "@/hooks/use-media-query"
import { DialogFooter } from "../ui/dialog"
import { DrawerFooter } from "../ui/drawer"
import { cn } from "@/lib/utils"

export default function DialogFooterWrapper({ children, className }: { children: React.ReactNode, className?: string }) {
  const isDesktop = useMediaQuery("(min-width:1024px)")
  if (isDesktop) {
    return <DialogFooter className={cn("px-0", className)}>
      {children}
    </DialogFooter>
  } else {
    return <DrawerFooter className={cn("px-0", className)}>
      {children}
    </DrawerFooter>
  }
}