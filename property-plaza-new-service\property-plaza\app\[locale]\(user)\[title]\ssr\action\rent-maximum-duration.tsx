import { formatDateSuffix, listingContract, ListingContractType } from "@/core/domain/listing/listing";
import { ItemWithSuffix } from "@/core/domain/utils/utils";
import { useTranslations } from "next-intl";

export default function RentMaximumDuration({ type, maxDuration, minDuration }: { type: ListingContractType, maxDuration: ItemWithSuffix, minDuration: ItemWithSuffix }) {
  const t = useTranslations("seeker")
  if (maxDuration.value == minDuration.value && maxDuration.suffix == minDuration.suffix) return <></>
  return <>
    {
      type == listingContract.rent && <>
        <p className="text-seekers-text-light inline-flex justify-between md:w-full gap-2">
          <span className="flex gap-2 items-center">
            <span className="w-2 h-2 bg-seekers-text-lighter rounded-full"></span>
            {t('listing.misc.maximumRent')}
          </span>
          <span className="font-semibold">
            {maxDuration.value} {
              formatDateSuffix(maxDuration.suffix as string) == "YEAR" ? t("misc.yearWithCount", { count: maxDuration.value }) : t("misc.month", { count: maxDuration.value })}
          </span>
        </p>
      </>
    }
  </>
}