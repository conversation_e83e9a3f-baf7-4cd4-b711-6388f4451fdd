import MainContentLayout from "@/components/seekers-content-layout/main-content-layout";
import { useTranslations } from "next-intl";
import { Calendar, Search, FileText } from "lucide-react";

export default function VerifyHowItWorks() {
  const t = useTranslations("verify");

  const howItWorks = [
    {
      icon: <Calendar className="w-6 h-6" />,
      title: t("howItWorks.steps.book.title"),
      description: t("howItWorks.steps.book.description"),
      result: t("howItWorks.steps.book.result")
    },
    {
      icon: <Search className="w-6 h-6" />,
      title: t("howItWorks.steps.inspect.title"),
      description: t("howItWorks.steps.inspect.description"),
      result: [
        t("howItWorks.steps.inspect.result.basic"),
        t("howItWorks.steps.inspect.result.smart"),
        t("howItWorks.steps.inspect.result.fullShield")
      ]
    },
    {
      icon: <FileText className="w-6 h-6" />,
      title: t("howItWorks.steps.report.title"),
      description: t("howItWorks.steps.report.description"),
      result: [
        t("howItWorks.steps.report.result.basic"),
        t("howItWorks.steps.report.result.smart"),
        t("howItWorks.steps.report.result.fullShield")
      ]
    }
  ];

  return (
    <section className="bg-seekers-foreground/50 py-12" aria-labelledby="how-it-works-title">
      <MainContentLayout>
        <div className="space-y-6">
          {/* Centered Header */}
          <div className="text-center space-y-2">
            <h2 id="how-it-works-title" className="text-3xl md:text-4xl font-bold text-seekers-text">
              {t("howItWorks.title")}
            </h2>
            <p className="text-base font-semibold tracking-[0.5%] text-seekers-text-light">
              {t("howItWorks.subtitle")}
            </p>
          </div>

          <div className="relative max-w-[1200px] mx-auto px-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 relative">
              {howItWorks.map((item, index) => (
                <div
                  key={index}
                  className="group relative bg-white p-6 rounded-2xl border border-gray-100
                  hover:border-seekers-primary/20 transition-all duration-300 shadow-sm hover:shadow-md
                  min-h-[200px]"
                >
                  <div className="flex flex-col h-full">
                    {/* Icon and number container */}
                    <div className="flex items-center gap-4 mb-4">
                      <div className="relative shrink-0">
                        <div className="w-12 h-12 bg-seekers-primary/10 rounded-xl flex items-center justify-center
                        text-seekers-primary group-hover:scale-110 transition-transform duration-300">
                          {item.icon}
                        </div>
                        <div className="absolute inset-0 bg-seekers-primary/5 blur-xl rounded-full
                        group-hover:blur-2xl transition-all duration-300" />
                      </div>
                      <span className="text-2xl font-bold text-seekers-primary/30
                      group-hover:text-seekers-primary transition-colors">
                        0{index + 1}
                      </span>
                    </div>

                    {/* Content */}
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 mb-3
                      group-hover:text-seekers-primary transition-colors duration-300">
                        {item.title}
                      </h3>
                      <p className="text-gray-600 text-sm leading-relaxed mb-4 whitespace-pre-line">
                        {item.description}
                      </p>

                    </div>
                  </div>

                  {/* Hover effect border */}
                  <div className="absolute inset-0 border-2 border-transparent
                  group-hover:border-seekers-primary/20 rounded-2xl transition-colors duration-300" />
                </div>
              ))}
            </div>
          </div>

          {/* Why Choose Section */}
          <div className="mt-12 bg-seekers-primary/5 rounded-lg p-6 md:p-8 text-center max-w-4xl mx-auto">
            <h3 className="text-xl md:text-2xl font-bold text-seekers-text mb-4">
              {t("howItWorks.whyChoose.title")}
            </h3>
            <p className="text-lg text-seekers-text-light">
              {t("howItWorks.whyChoose.description")}
            </p>
          </div>
        </div>
      </MainContentLayout>
    </section>
  );
}
