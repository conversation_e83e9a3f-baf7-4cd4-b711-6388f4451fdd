"use client"

import { useState } from "react"
import { useTranslations } from "next-intl"
import { MapPin, Mail, Phone } from "lucide-react"
import MainContentLayout from "@/components/seekers-content-layout/main-content-layout"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"
import AboutUsImage from '@/public/about-us-image.webp'
import OfficeBuildingImage from '@/public/office-building.webp'
export default function AboutUsContent() {
  const t = useTranslations("seeker")
  const [activeTab, setActiveTab] = useState("company")

  return (
    <>
      <div className="w-full bg-white">
        {/* Hero Section met semantische HTML5 tags */}
        <section aria-label="About Property Plaza Hero" className="relative w-full h-[400px] md:h-[500px]">
          <Image
            src={AboutUsImage}
            alt="Property Plaza"
            fill
            className="object-cover"
            style={{ objectFit: "cover" }}
            priority
          />
          <div className="absolute inset-0 bg-black/40 flex items-center justify-center">
            <div className="text-center px-4">
              <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">{t('aboutUs.hero.title')}</h1>
              <p className="text-xl text-white max-w-3xl mx-auto">
                {t('aboutUs.hero.description')}
              </p>
            </div>
          </div>
        </section>

        {/* Navigation Tabs */}
        <MainContentLayout>
          <div className="max-w-7xl mx-auto px-4 py-8">
            <div className="flex flex-wrap border-b border-gray-200 mb-8">
              <button
                onClick={() => setActiveTab("company")}
                className={`mr-8 py-4 text-lg font-medium border-b-2 transition-colors ${activeTab === "company"
                  ? "border-seekers-primary text-seekers-primary"
                  : "border-transparent text-gray-500 hover:text-seekers-primary"
                  }`}
              >
                {t('aboutUs.tabs.company')}
              </button>
              <button
                onClick={() => setActiveTab("team")}
                className={`mr-8 py-4 text-lg font-medium border-b-2 transition-colors ${activeTab === "team"
                  ? "border-seekers-primary text-seekers-primary"
                  : "border-transparent text-gray-500 hover:text-seekers-primary"
                  }`}
              >
                {t('aboutUs.tabs.team')}
              </button>
              <button
                onClick={() => setActiveTab("mission")}
                className={`py-4 text-lg font-medium border-b-2 transition-colors ${activeTab === "mission"
                  ? "border-seekers-primary text-seekers-primary"
                  : "border-transparent text-gray-500 hover:text-seekers-primary"
                  }`}
              >
                {t('aboutUs.tabs.mission')}
              </button>
            </div>

            {/* Company Section */}
            {activeTab === "company" && (
              <div className="grid md:grid-cols-2 gap-12 items-center">
                <div>
                  <h2 className="text-3xl font-bold mb-6 text-gray-800">{t('aboutUs.story.companyTitle')}</h2>
                  <p className="text-gray-600 mb-4">
                    {t('aboutUs.story.paragraph1')}
                  </p>
                  <p className="text-gray-600 mb-4">
                    {t('aboutUs.story.paragraph2')}
                  </p>
                  <p className="text-gray-600 mb-6">
                    {t('aboutUs.story.paragraph3')}
                  </p>
                  <div className="flex flex-wrap gap-4">
                    <Button
                      asChild
                      variant="default-seekers"
                    >
                      <Link href="/contact">{t('aboutUs.hero.contactUs')}</Link>
                    </Button>
                    <Button
                      asChild
                      variant="outline"
                    >
                      <Link href="/s/all">{t('aboutUs.hero.browseProperties')}</Link>
                    </Button>
                  </div>
                </div>
                <div className="relative h-[400px] rounded-lg overflow-hidden">
                  <Image
                    src={OfficeBuildingImage}
                    alt="Property Plaza Office"
                    fill
                    className="object-cover"
                  />
                </div>
              </div>
            )}

            {/* Team Section */}
            {activeTab === "team" && (
              <div>
                <div className="text-center mb-12">
                  <h2 className="text-3xl font-bold mb-4 text-gray-800">{t('aboutUs.team.title')}</h2>
                  <p className="text-gray-600 max-w-3xl mx-auto">
                    {t('aboutUs.team.description')}
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                  {[
                    {
                      name: t('aboutUs.team.members.rt.name'),
                      position: t('aboutUs.team.roles.ceo'),
                      bio: t('aboutUs.team.members.rt.bio'),
                      image: "/team-member-ricardo-2.jpg"
                    },
                    {
                      name: t('aboutUs.team.members.thijs.name'),
                      position: t('aboutUs.team.roles.cto'),
                      bio: t('aboutUs.team.members.thijs.bio'),
                      image: "/team-member-thijs-2.jpg"
                    },
                    {
                      name: t('aboutUs.team.members.joost.name'),
                      position: t('aboutUs.team.roles.marketing'),
                      bio: t('aboutUs.team.members.joost.bio'),
                      image: "/team-member-joost.jpg"
                    },
                    {
                      name: t('aboutUs.team.members.dennis.name'),
                      position: t('aboutUs.team.roles.propertySpecialist'),
                      bio: t('aboutUs.team.members.dennis.bio'),
                      image: "/team-member-dennis.jpg"
                    },
                    {
                      name: t('aboutUs.team.members.andrea.name'),
                      position: t('aboutUs.team.roles.propertySpecialist'),
                      bio: t('aboutUs.team.members.andrea.bio'),
                      image: "/team-member-andrea.jpg"
                    },
                    {
                      name: t('aboutUs.team.members.natha.name'),
                      position: t('aboutUs.team.roles.propertySpecialist'),
                      bio: t('aboutUs.team.members.natha.bio'),
                      image: "/team-member-natha.jpg"
                    },
                    {
                      name: t('aboutUs.team.members.aditya.name'),
                      position: t('aboutUs.team.roles.frontend'),
                      bio: t('aboutUs.team.members.aditya.bio'),
                      image: "/team-member-aditya.jpg"
                    },
                    {
                      name: t('aboutUs.team.members.anjas.name'),
                      position: t('aboutUs.team.roles.backend'),
                      bio: t('aboutUs.team.members.anjas.bio'),
                      image: "/team-member-anjas.jpg"
                    },
                    {
                      name: t('aboutUs.team.members.nuni.name'),
                      position: t('aboutUs.team.roles.backend2'),
                      bio: t('aboutUs.team.members.nuni.bio'),
                      image: "/team-member-nuni.jpg"
                    },
                    {
                      name: t('aboutUs.team.members.rizki.name'),
                      position: t('aboutUs.team.roles.tester'),
                      bio: t('aboutUs.team.members.rizki.bio'),
                      image: "/team-member-rizki.jpg"
                    }
                  ].map((member, index) => (
                    <div
                      key={index}
                      className="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow"
                    >
                      <div className="flex justify-center pt-6">
                        <div className="relative !h-[180px] !w-[180px] rounded-full overflow-hidden border-4 border-seekers-primary/10">
                          <Image src={member.image} alt={member.name} fill />
                        </div>
                      </div>
                      <div className="p-6">
                        <h3 className="text-xl font-semibold mb-1 text-gray-800">{member.name}</h3>
                        <p className="text-seekers-primary font-medium mb-3">{member.position}</p>
                        <p className="text-gray-600 text-sm mb-4">{member.bio}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Mission & Vision Section */}
            {activeTab === "mission" && (
              <div className="grid md:grid-cols-2 gap-12">
                <div className="bg-seekers-foreground/10 p-8 rounded-lg">
                  <div className="w-16 h-16 bg-seekers-primary rounded-full flex items-center justify-center mb-6">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-white"
                    >
                      <path d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z" />
                    </svg>
                  </div>
                  <h3 className="text-2xl font-bold mb-4 text-gray-800">{t('aboutUs.mission.ourMission.title')}</h3>
                  <p className="text-gray-600 mb-4">
                    {t('aboutUs.mission.description')}
                  </p>
                  <p className="text-gray-600">
                    {t('aboutUs.mission.ourMission.additionalText')}
                  </p>
                </div>

                <div className="bg-seekers-foreground/10 p-8 rounded-lg">
                  <div className="w-16 h-16 bg-seekers-primary rounded-full flex items-center justify-center mb-6">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-white"
                    >
                      <circle cx="12" cy="12" r="10" />
                      <path d="m16 10-4 4-4-4" />
                    </svg>
                  </div>
                  <h3 className="text-2xl font-bold mb-4 text-gray-800">{t('aboutUs.mission.ourVision.title')}</h3>
                  <p className="text-gray-600 mb-4">
                    {t('aboutUs.mission.ourVision.description')}
                  </p>
                  <p className="text-gray-600">
                    {t('aboutUs.mission.ourVision.additionalText')}
                  </p>
                </div>

                <div className="md:col-span-2 mt-8">
                  <h3 className="text-2xl font-bold mb-6 text-gray-800">{t('aboutUs.mission.ourCoreValues.title')}</h3>
                  <div className="grid md:grid-cols-3 gap-6">
                    {[
                      {
                        title: t('aboutUs.mission.values.global.title'),
                        description: t('aboutUs.mission.values.global.description')
                      },
                      {
                        title: t('aboutUs.mission.values.trust.title'),
                        description: t('aboutUs.mission.values.trust.description')
                      },
                      {
                        title: t('aboutUs.mission.values.quality.title'),
                        description: t('aboutUs.mission.values.quality.description')
                      },
                      {
                        title: t('aboutUs.mission.values.community.title'),
                        description: t('aboutUs.mission.values.community.description')
                      },
                      {
                        title: t('aboutUs.mission.values.innovation.title'),
                        description: t('aboutUs.mission.values.innovation.description')
                      },
                      {
                        title: t('aboutUs.mission.values.personalization.title'),
                        description: t('aboutUs.mission.values.personalization.description')
                      }
                    ].map((value, index) => (
                      <div
                        key={index}
                        className="border border-gray-200 p-6 rounded-lg hover:border-seekers-primary transition-colors"
                      >
                        <h4 className="text-xl font-semibold mb-3 text-gray-800">{value.title}</h4>
                        <p className="text-gray-600">{value.description}</p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        </MainContentLayout>

        {/* Contact Section */}
        <div className="bg-seekers-foreground/10 py-16 mt-16">
          <MainContentLayout>
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4 text-gray-800">{t('aboutUs.contact.title')}</h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                {t('aboutUs.cta.description')}
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              <div className="bg-white p-6 rounded-lg shadow-md flex flex-col items-center text-center">
                <div className="w-12 h-12 bg-seekers-primary rounded-full flex items-center justify-center mb-4">
                  <MapPin className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-xl font-semibold mb-2 text-gray-800">{t('aboutUs.contact.visitUs.title')}</h3>
                <p className="text-gray-600 whitespace-pre-line">
                  {t('aboutUs.contact.visitUs.address')}
                </p>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-md flex flex-col items-center text-center">
                <div className="w-12 h-12 bg-seekers-primary rounded-full flex items-center justify-center mb-4">
                  <Mail className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-xl font-semibold mb-2 text-gray-800">{t('aboutUs.contact.emailUs.title')}</h3>
                <p className="text-gray-600 mb-2">{t('aboutUs.contact.emailUs.general')}</p>
                <a href="mailto:<EMAIL>" className="text-seekers-primary hover:underline">
                  {t('aboutUs.contact.emailUs.generalEmail')}
                </a>
                <p className="text-gray-600 mt-2 mb-2">{t('aboutUs.contact.emailUs.listings')}</p>
                <a href="mailto:<EMAIL>" className="text-seekers-primary hover:underline">
                  {t('aboutUs.contact.emailUs.listingsEmail')}
                </a>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-md flex flex-col items-center text-center">
                <div className="w-12 h-12 bg-seekers-primary rounded-full flex items-center justify-center mb-4">
                  <Phone className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-xl font-semibold mb-2 text-gray-800">{t('aboutUs.contact.callUs.title')}</h3>
                <p className="text-gray-600 mb-2">{t('aboutUs.contact.callUs.officeHours')}</p>
                <p className="text-gray-600 mt-4 mb-2">{t('aboutUs.contact.callUs.whatsapp')}</p>
                <a href="https://wa.me/6281234567890" className="text-seekers-primary hover:underline">
                  {t('aboutUs.contact.callUs.whatsappNumber')}
                </a>
              </div>
            </div>

            <div className="flex justify-center mt-12">
              <Button asChild variant="default-seekers" size="lg">
                <Link href="/s/all">{t('aboutUs.cta.findProperty')}</Link>
              </Button>
            </div>
          </MainContentLayout>
        </div>
      </div>
    </>
  )
} 