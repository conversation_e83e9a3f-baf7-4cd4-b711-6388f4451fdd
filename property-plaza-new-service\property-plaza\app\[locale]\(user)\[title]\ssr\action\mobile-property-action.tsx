
import { isListingActive, ListingContractType } from "@/core/domain/listing/listing"
import { ItemWithSuffix } from "@/core/domain/utils/utils"
import PriceInfo from "./price-info"
import ContactOwner from "./contact-owner"
import RentMinimumDuration from "./rent-minimum-duration"
import ShareAction from "./share-action"
import SaveAction from "./save-action"
import RentMaximumDuration from "./rent-maximum-duration"
import MobilePropertyActionDetail from "./mobile-property-action-detail"
import { Badge } from "@/components/ui/badge"
import AvailableAt from "./available-at"
import { useTranslations } from "next-intl"
import PropertyOwner from "./property-owner"

export default async function MobilePropertyAction({ price, type, minDuration, maxDuration, propertyId, ownerId, isFavorited, isNegotiable, isActiveListing, middlemanId, availableAt, owner, middleman, chatCount }: {
  price: number,
  type: ListingContractType
  minDuration: ItemWithSuffix,
  maxDuration: ItemWithSuffix,
  propertyId: string,
  ownerId: string,
  middlemanId?: string
  currency?: string,
  isFavorited?: boolean,
  isNegotiable?: boolean,
  isActiveListing: boolean,
  availableAt: string | null,
  owner?: {
    ownerId: string,
    ownerName: string,
    ownerProfileUrl: string
  },
  middleman?: {
    middlemanId: string,
    middlemanName: string,
    middlemanProfileUrl: string
  },
  chatCount?: number,

}) {
  const t = useTranslations("seeker")
  return <MobilePropertyActionDetail
    overview={
      <div className="w-full">
        <div className="flex justify-between items-center">
          <PriceInfo price={price} type={type} maxDuration={maxDuration} minDuration={minDuration} isNegotiable={isNegotiable} />
          <ContactOwner ownerId={ownerId} propertyId={propertyId} isActiveListing={isActiveListing} middlemanId={middlemanId} />
        </div>
        <div className="flex justify-between">
          <div className="flex gap-2">
            <ShareAction />
            <SaveAction propertyId={propertyId} isFavorited={isFavorited} />
          </div>
        </div>
      </div >
    }
  >
    <p><span className="">{t('misc.propertyType')}</span>  <span className="font-bold lowercase">{type}</span> </p>
    <AvailableAt availableAt={availableAt} />
    {type == "RENT" &&
      <div className="border-t border-t-seekers-text-lighter border-b border-b-seekers-text-lighter py-4 flex flex-col">
        <p className="text-xs font-semibold text-seekers-text-light !mb-2 ">{t('misc.rentalTerms')}</p>
        <RentMinimumDuration type={type} minDuration={minDuration} />
        <RentMaximumDuration type={type} maxDuration={maxDuration} minDuration={minDuration} />
      </div>
    }
    {owner && <PropertyOwner
      owner={{ name: owner.ownerName || t("misc.ownerProperty"), image: owner.ownerProfileUrl }}
      middleman={middleman ? { name: middleman?.middlemanName, image: middleman.middlemanProfileUrl } : undefined}
    />
    }
    <div className="space-y-2">
      {chatCount != undefined && chatCount > 0 &&
        <p className="text-center text-xs font-medium text-seekers-text-light">{t('listing.detail.contactCount', { count: chatCount })}</p>
      }
    </div>
  </MobilePropertyActionDetail>
}