"use client"

import { Card, Card<PERSON>ontent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { useTranslations } from "next-intl";
import BillingHistoryDataTable from "./data-table";

export default function BillingHistory({ conversionRate }: { conversionRate: { [key: string]: string } }) {
  const t = useTranslations("seeker")
  return <Card className="border-[#C19B67]/20">
    <CardHeader>
      <CardTitle className="text-[#C19B67]">{t("setting.subscriptionStatus.billing.billingHistory.title")}</CardTitle>
      <CardDescription>{t("setting.subscriptionStatus.billing.billingHistory.description")}</CardDescription>
    </CardHeader>
    <CardContent>
      <BillingHistoryDataTable conversionRate={conversionRate} />
    </CardContent>
  </Card>
}