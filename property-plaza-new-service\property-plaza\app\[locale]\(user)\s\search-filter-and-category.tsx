"use client"
import { Home, Slash } from "lucide-react";
import { useTranslations } from "next-intl";
import { cn } from "@/lib/utils";
import { Breadcrumb, BreadcrumbItem, B<PERSON><PERSON>rumbList, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import MainContentLayout from "@/components/seekers-content-layout/main-content-layout";
import FilterHeader from "./filter-header";
import SearchResultCount from "./search-result-count";
import Link from "next/link";
import useSeekersSearch from "@/hooks/use-seekers-search";


function capitalizeFirstLetter(text: string): string {
  return text.charAt(0).toUpperCase() + text.slice(1);
};

export default function SearchFilterAndCategory({ query, types, conversions }: {
  query: string,
  types: string,
  conversions: { [key: string]: number }
}) {
  const t = useTranslations("seeker")
  const { propertyTypeFormatHelper } = useSeekersSearch()
  return <MainContentLayout className={cn("max-sm:hidden flex max-sm:flex-col items-center space-y-0 h-[100px] max-lg:!h-[80px]  gap-4 sticky md:top-[90px] lg:top-[104px] xl:top-[114px] z-[1] bg-white")}>
    <div className="flex-grow space-y-2">
      <SearchResultCount />
      <Breadcrumb className=" hidden md:block ">
        <BreadcrumbList className="space-x-4 sm:gap-0 flex-nowrap">
          <BreadcrumbItem className="text-seekers-text font-medium text-sm">
            <Link href={"/"} className="flex gap-2.5 items-center">
              <Home className="w-4 h-4" strokeWidth={1} />
              {/* {t('misc.home')} */}
            </Link>
          </BreadcrumbItem>
          <BreadcrumbSeparator className="text-seekers-text font-medium text-sm w-3 h-fit text-center">
            /
          </BreadcrumbSeparator>
          {query == "all" ? <></> :
            <>
              <BreadcrumbItem className="capitalize text-seekers-text font-medium text-sm">

                {query.replaceAll("-", " ").replaceAll("--", " ")}
              </BreadcrumbItem>
              <BreadcrumbSeparator className="text-seekers-text font-medium text-sm w-3 h-fit text-center">
                /
              </BreadcrumbSeparator>
            </>
          }
          <BreadcrumbItem className="text-seekers-text font-semibold text-sm line-clamp-1">
            {types.split(",").includes("all") ?
              <>
                {t('misc.allProperty')}
              </>
              :
              <>
                {propertyTypeFormatHelper(
                  types.replaceAll("-", " ")
                    .replaceAll("--", " ")
                    .split(","))
                  .toString().replaceAll(",", ", ")}
              </>
            }
          </BreadcrumbItem>

        </BreadcrumbList>
      </Breadcrumb>
    </div>
    <FilterHeader conversions={conversions} />
  </MainContentLayout>
}
