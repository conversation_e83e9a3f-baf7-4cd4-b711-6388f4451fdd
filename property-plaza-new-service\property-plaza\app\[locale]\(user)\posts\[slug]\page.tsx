export const revalidate = 3600;

import { getPostByCategory, getPostBySlug } from "@/core/services/sanity/services";
import BlogContent from "./content";
import MoreArticles from "./more-articles";
import { notFound } from "next/navigation";
import { Metadata } from "next";
import PostBreadCrumb from "../bread-crumb";
import { getCurrencyConversion } from "@/core/infrastructures/currency-converter/api";
import { getLocale } from "next-intl/server";
import { postUrl } from "@/lib/constanta/route";


export async function generateMetadata({ params }: { params: { slug: string } }): Promise<Metadata> {
  const locale = await getLocale()
  const blogContent = await getPostBySlug(params.slug)
  const baseUrl = process.env.USER_DOMAIN || "https://www.property-plaza.com/"

  return {
    title: blogContent?.title || params.slug.replaceAll("-", " "),
    description: blogContent?.metadata || "",
    openGraph: {
      title: blogContent?.title || params.slug.replaceAll("-", " "),
      description: blogContent?.metadata || "",
      images: [
        {
          url: blogContent.mainImage.asset.url, // MUST be a full URL like "https://cdn.example.com/img.jpg"
          width: 1200,
          height: 630,
          alt: blogContent.title,
        }
      ],
      type: "article",
      url: baseUrl + locale + postUrl + "/" + params.slug
    },
    alternates: {
      canonical: baseUrl + locale + postUrl + "/" + params.slug,
      languages: {
        en: baseUrl + "en" + postUrl + "/" + params.slug,
        id: baseUrl + "id" + postUrl + "/" + params.slug,
        "x-default": baseUrl + postUrl.replace("/", "") + "/" + params.slug,
      }
    },
    twitter: {
      card: "summary_large_image",
      title: blogContent?.title || params.slug,
      description: blogContent?.metadata || "",
      images: [baseUrl + "og.jpg"],
    },
    robots: {
      index: true,
      follow: true,
    }
  }
}

export default async function PostDetail({ params }: { params: { slug: string } }) {
  const blogContent = await getPostBySlug(params.slug)
  if (!blogContent) return notFound()
  const defaultCategory = "real-estate"
  const moreContent = await getPostByCategory(blogContent?.category?._id || defaultCategory, blogContent._id.toString())

  const conversionRates = await getCurrencyConversion()

  return <>
    <PostBreadCrumb title={blogContent.title} />
    <BlogContent blogContent={blogContent} conversions={conversionRates.data} />
    <MoreArticles moreContent={moreContent} />
  </>
}