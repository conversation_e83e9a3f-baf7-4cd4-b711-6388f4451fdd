import MainContentLayout from "@/components/seekers-content-layout/main-content-layout";
import MessageBreadCrumb from "./bread-crumb";
import { getLocale, getTranslations } from "next-intl/server";
import { Metadata } from "next";
import ChatList from "./chat-list";
import { ChatDetail } from "./chat-detail";
import ParticipantDetail from "./participant-detail";
import { messagesUrl, seekersMessageUrl } from "@/lib/constanta/route";
import { routing } from "@/lib/locale/routing";

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations("seeker")
  const locale = await getLocale() || routing.defaultLocale
  const baseUrl = process.env.USER_DOMAIN || "https://www.property-plaza.com/"
  return {
    title: t("metadata.message.title"),
    description: t("metadata.message.description"),
    alternates: {
      canonical: baseUrl + locale + seekersMessageUrl,
      languages: {
        en: baseUrl + "en" + seekersMessageUrl,
        id: baseUrl + "id" + seekersMessageUrl,
        "x-default": baseUrl + seekersMessageUrl.replace("/", ""),
      }
    },
    openGraph: {
      title: t("metadata.message.title"),
      description: t("metadata.message.description"),
      images: [
        {
          url: baseUrl + "og.png",
          width: 1200,
          height: 630,
          alt: "Property Plaza",
        }
      ],
      type: "website",
      url: baseUrl + locale + seekersMessageUrl,
      countryName: "Indonesia",
      emails: "<EMAIL>",
      locale: locale,
      alternateLocale: routing.locales,
      siteName: "Property plaza"
    },
    applicationName: "Property plaza",
    twitter: {
      card: "summary_large_image",
      title: t("metadata.message.title"),
      description: t("metadata.message.description"),
      images: [baseUrl + "og.jpg"],
    },

    robots: {
      index: false,
      follow: false,
      nocache: false,
    },
  }
}



export default function MessagePage() {
  return <div className="h-full max-sm:space-y-6 md:flex md:flex-col items-start md:overflow-hidden max-h-full md:gap-6 ">
    <MessageBreadCrumb />
    <MainContentLayout className="flex gap-8 space-y-0 w-full max-sm:pb-4 md:max-h-[calc(100%-68px-24px)] flex-grow max-md:px-0 md:pr-0"> {/* calculation formula: 100% - breadcrumbHeight - gap */}
      <ChatList />
      <ChatDetail />
      <ParticipantDetail />
    </MainContentLayout>
  </div>
}