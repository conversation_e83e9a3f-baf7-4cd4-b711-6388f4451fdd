"use client"
import { useToast } from "@/hooks/use-toast"
import { useTranslations } from "next-intl"
import useChangePasswordFormSchema from "./use-change-password-form.schema"
import { z } from "zod"
import { useResetPassord } from "@/core/applications/mutations/auth/use-reset-passoword"
import { useVerifyResetPassword } from "@/core/applications/mutations/auth/use-verify-reset-password"
import { useEffect } from "react"
import { ResetPasswordDto, VerifyResetPasswordDto } from "@/core/infrastructures/auth/dto"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { Form } from "@/components/ui/form"
import PasswordInput from "@/components/input-form/password-input"
import { Button } from "@/components/ui/button"
import useSearchParamWrapper from "@/hooks/use-search-param-wrapper"
import { useRouter } from 'nextjs-toploader/app';

export default function ChangePasswordForm({ email, token, isSeeker = true }: { email: string, token: string, isSeeker?: boolean }) {
  const t = useTranslations("universal")
  const { removeQueryParam } = useSearchParamWrapper()
  const { toast } = useToast()
  const router = useRouter()
  const formSchema = useChangePasswordFormSchema()
  type formSchemaType = z.infer<typeof formSchema>
  const useResetPasswordMutation = useResetPassord()
  const useVerifyRequestForgetPasswordMutation = useVerifyResetPassword()
  const form = useForm<formSchemaType>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      password: "",
      confirmPassword: ""
    }
  })
  useEffect(() => {
    const verifyEmail = async () => {
      const data: VerifyResetPasswordDto = {
        email, token
      }
      try {
        await useVerifyRequestForgetPasswordMutation.mutateAsync(data)
      } catch (error: any) {
        toast({
          title: t("error.resetPassword.title"),
          description: error.response.data.message,
          variant: "destructive"
        })
        removeQueryParam(["email", "token"])
      }

    }
    if (email && token) {
      verifyEmail()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [email, token])

  async function onSubmit(values: formSchemaType) {
    const data: ResetPasswordDto = {
      email: email,
      token: token,
      password: values.password,
      confirm_password: values.confirmPassword
    }
    try {
      await useResetPasswordMutation.mutateAsync(data)
    } catch (error: any) {
      toast({
        title: t("error.resetPassword.title"),
        description: error.response.data.message,
        variant: "destructive"
      })
    }
    router.push("/")

  }
  return <Form {...form}>
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
      <div className="space-y-2 text-center">
        <h1 className="text-2xl font-semibold text-center">{t("form.title.resetPassword")}</h1>
      </div>
      <div className="space-y-2">
        <PasswordInput form={form} name="password" label={t("form.label.password")} placeholder={t('form.placeholder.basePlaceholder', { field: `${t("form.field.password")}` })} />
        <PasswordInput form={form} name="confirmPassword" label={t("form.label.confirmPassword")} placeholder={t('form.placeholder.basePlaceholder', { field: `${t("form.field.confirmPassword")}` })} />
      </div>
      <Button
        className="w-full"
        variant={"default-seekers"}
        loading={useResetPasswordMutation.isPending}>
        {t('cta.changePassword')}
      </Button>
    </form>
  </Form >
}