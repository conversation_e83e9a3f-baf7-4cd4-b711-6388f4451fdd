import MainContentLayout from "@/components/seekers-content-layout/main-content-layout";
import PlansBreadCrumb from "./bread-crumb";
import { Metadata } from "next";
import { getLocale, getTranslations } from "next-intl/server";
import SubscriptionContent from "./content"
import { getCurrencyConversion } from "@/core/infrastructures/currency-converter/api";
import { getAllSubscriptionPackagesService } from "@/core/infrastructures/subscription/service";
import { Subscription } from "@/core/domain/subscription/subscription";
import { plansUrl } from "@/lib/constanta/route";
import { routing } from "@/lib/locale/routing";

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations("seeker")
  const locale = await getLocale() || routing.defaultLocale
  const baseUrl = process.env.USER_DOMAIN || "https://www.property-plaza.com/"
  return {
    title: t("metadata.subsriptionPlan.title"),
    description: t("metadata.subsriptionPlan.description"),
    alternates: {
      canonical: baseUrl + locale + plansUrl,
      languages: {
        en: baseUrl + "en" + plansUrl,
        id: baseUrl + "id" + plansUrl,
        "x-default": baseUrl + plansUrl.replace("/", "")
      }
    },
    openGraph: {
      title: t("metadata.subsriptionPlan.title"),
      description: t("metadata.subsriptionPlan.description"),
      images: [
        {
          url: baseUrl + "og.png",
          width: 1200,
          height: 630,
          alt: "Property Plaza",
        }
      ],
      type: "website",
      url: baseUrl + locale + plansUrl,
      countryName: "Indonesia",
      emails: "<EMAIL>",
      locale: locale,
      alternateLocale: routing.locales,
      siteName: "Property plaza"
    },
    applicationName: "Property plaza",
    twitter: {
      card: "summary_large_image",
      title: t("metadata.subsriptionPlan.title"),
      description: t("metadata.subsriptionPlan.description"),
      images: [baseUrl + "og.jpg"],
    },

    robots: {
      index: true,
      follow: true,
      nocache: false,
    },
  }
}


export default async function PlansPage() {
  const conversionRates = await getCurrencyConversion("EUR")
  const subscriptionPackages = await getAllSubscriptionPackagesService()
  const data = subscriptionPackages.data
  const t = await getTranslations("seeker")

  return <>
    <PlansBreadCrumb />
    <MainContentLayout className="space-y-8 my-8 mb-14 max-sm:px-0">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">{t("setting.subscriptionStatus.subscription.title")}</h1>
        <h2 className="text-muted-foreground mt-2">{t("setting.subscriptionStatus.subscription.description")}</h2>
      </div>
      <SubscriptionContent SubscriptionPackages={data as Subscription[] || []} conversionRate={conversionRates.data} />
    </MainContentLayout>
  </>
}