"use client"
import Link from "next/link";
import DialogFooterWrapper from "../dialog-wrapper/dialog-footer.wrapper";
import DialogHeaderWrapper from "../dialog-wrapper/dialog-header-wrapper";
import DialogWrapper from "../dialog-wrapper/dialog-wrapper";
import { But<PERSON> } from "../ui/button";
import { useTranslations } from "next-intl";

export default function FollowInstagramPopUp({ open, setOpen, trigger }: { open: boolean, setOpen: (isOpen: boolean) => void, trigger: React.ReactNode }) {
  const t = useTranslations("universal")
  return <DialogWrapper
    open={open}
    setOpen={setOpen}
    openTrigger={trigger}

  >
    <DialogHeaderWrapper>
      <h3 className="text-base font-bold text-seekers-text">
        {t('popup.followInstagram.title')}
      </h3>
    </DialogHeaderWrapper>
    <div>
      <p>{t('popup.followInstagram.description')}</p>
    </div>
    <DialogFooterWrapper>
      <Button asChild className="w-full" variant={"default-seekers"}>
        <Link href={"https://www.instagram.com/join.propertyplaza/"}>
          {t('cta.followUsOnInstagram')}
        </Link>
      </Button>
    </DialogFooterWrapper>
  </DialogWrapper>
}