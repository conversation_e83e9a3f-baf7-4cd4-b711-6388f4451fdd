import { useSeekerFilterStore } from "@/stores/seekers-filter-2.store"
import FilterContentLayout from "./filter-content-layout"
import { BaseSelectInputValue } from "@/types/base"
import { cn } from "@/lib/utils"
import CheckboxFilterItem from "./checkbox-filter-item"
import { useTranslations } from "next-intl"

export default function MinimumContractDuration() {
  const t = useTranslations("seeker")
  const { minimumContract, setMinimumContract } = useSeekerFilterStore(state => state)
  const contractDruration: BaseSelectInputValue<string>[] = [
    {
      id: "1",
      content: t('listing.filter.minimumContract.optionOne.title'),
      value: "ANY"
    },
    {
      id: "2",
      content: t('listing.filter.minimumContract.optionTwo.title'),
      value: "LOWER_THAN_1"
    },
    {
      id: "3",
      content: t('listing.filter.minimumContract.optionThree.title'),
      value: "BETWEEN_1_3"
    },
    {
      id: "4",
      content: t('listing.filter.minimumContract.optionFour.title'),
      value: "BETWEEN_3_5"
    },
    {
      id: "5",
      content: t('listing.filter.minimumContract.optionFive.title'),
      value: "GREATER_THAN_5"
    },
  ]
  return <FilterContentLayout title={t('listing.filter.others.minimumContract.title')}>
    <div className="flex gap-2 max-sm:flex-wrap">
      {contractDruration.map(item => <CheckboxFilterItem
        key={item.id}
        item={item}
        setValue={setMinimumContract}
        isActive={minimumContract == item.value}
        className={cn(
          "max-sm:!w-fit !w-full text-center items-center justify-center"
        )}
      />
      )}
    </div>
  </FilterContentLayout>
}