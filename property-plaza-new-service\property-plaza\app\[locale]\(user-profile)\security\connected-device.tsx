import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Laptop } from "lucide-react"
import { useTranslations } from "next-intl"

export default function ConnectedDevice() {
  const t = useTranslations("seeker")
  const connectedDevices = [
    {
      name: "MacBook Pro",
      type: "Desktop",
      lastActive: "Active now",
      isCurrent: true,
    },
    {
      name: "iPhone 15",
      type: "Mobile",
      lastActive: "2 hours ago",
      isCurrent: false,
    },
    {
      name: "iPad Air",
      type: "Tablet",
      lastActive: "3 days ago",
      isCurrent: false,
    },
  ]
  return <Card>
    <CardHeader className="flex max-sm:flex-col max-sm:items-start max-sm:gap-4 max-sm:pb-4 flex-row items-center justify-between space-y-0 pb-2">
      <div className="space-y-1">
        <CardTitle className="text-seekers-primary flex items-center">
          <Laptop className="mr-2 h-4 w-4" />
          {t("setting.profile.security.connectedDevice.title")}
        </CardTitle>
        <CardDescription>{t("setting.profile.security.connectedDevice.description")}</CardDescription>
      </div>
      <Button
        variant="outline"
        className="border-red-500 text-red-500 hover:bg-red-50 hover:text-red-500"
        size={"sm"}
      >
        {t("cta.signOutAll")}
      </Button>
    </CardHeader>
    <CardContent>
      <div className="space-y-4">
        {connectedDevices.map((device, index) => (
          <div key={index} className="flex items-center justify-between border-b last:border-0 pb-4 last:pb-0">
            <div className="space-y-1">
              <div className="flex items-center">
                <span className="font-medium">{device.name}</span>
                {device.isCurrent && (
                  <Badge variant="outline" className="ml-2  bg-green-500/10 text-green-500">
                    {t("setting.profile.security.connectedDevice.currentDevice")}
                  </Badge>
                )}
              </div>
              <div className="text-sm text-muted-foreground">
                {t("setting.profile.security.connectedDevice.lastActive")}: {device.lastActive}
              </div>
            </div>
            {!device.isCurrent && (
              <Button
                variant="link"
                className="text-red-500"
                size={"sm"}
              >
                {t("cta.signOutDevice")}
              </Button>
            )}
          </div>
        ))}
      </div>
    </CardContent>
  </Card>
}