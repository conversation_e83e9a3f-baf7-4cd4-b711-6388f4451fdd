"use client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuCheckboxItem, DropdownMenuContent, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import { messageSeekerCategory } from "@/core/domain/messages/messages"
import { useDebounce } from "@/hooks/use-debounce"
import { MixerHorizontalIcon, QuestionMarkIcon } from "@radix-ui/react-icons"
import { useTranslations } from "next-intl"
import { useEffect, useState } from "react"
import useSearchParamWrapper from "@/hooks/use-search-param-wrapper"
import StartChatWithCS from "./start-chat-with-cs-dialog"
import TooltipWrapper from "@/components/tooltop-wrapper/tooltip-wrapper"
import { convertFilterToText } from "./message-helper"

export default function SearchAndFilterChat() {
  const t = useTranslations("seeker")
  const filter = messageSeekerCategory
  const [search, setSearch] = useState("")
  const debounce = useDebounce(search)
  const { createQueryString, searchParams, removeQueryParam } = useSearchParamWrapper()
  useEffect(() => {
    createQueryString("search", debounce.trim())
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debounce])
  const handleAddFilter = (value: string) => {
    let filter = searchParams.get("filter") || ""
    if (!filter) {
      createQueryString("filter", value)
      return
    }
    const isContain = filter?.includes(value)
    if (isContain) {
      const newFilter = filter.replaceAll(",", " ").replace(value, "").trim()
      const finalFilter = newFilter.replace(/\s+/g, ' ')
      if (newFilter.length < 1) {
        removeQueryParam(["filter"])
      } else {
        const filterArray = finalFilter.split(" ") as string[]
        filterArray.filter(value => value !== "")
        createQueryString("filter", filterArray.toString())
      }
    } else {
      const filterArray = filter.split(",")
      filterArray.push(value)
      createQueryString("filter", filterArray.toString())
    }
  }
  return <div className={`flex gap-2 justify-between w-full `}>
    <Input placeholder={t('message.searchChat')} className="flex-grow h-8 border-seekers-text-lighter placeholder:text-seekers-text-lighter" value={search} onChange={e => setSearch(e.target.value)} />
    <DropdownMenu >
      <DropdownMenuTrigger asChild>
        <Button

          variant="outline"
          size="sm"
          className="ml-auto h-8 flex text-seekers-text border-seekers-text-lighter"
        >
          <MixerHorizontalIcon className="mr-2 h-4 w-4" />
          {t('cta.filter')}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-fit">
        {Object.values(filter).map((column) => {
          return (
            <DropdownMenuCheckboxItem
              key={column}
              className="capitalize"
              checked={!searchParams.get("filter")?.includes(column)}
              onCheckedChange={() => handleAddFilter(column)}
            >
              {convertFilterToText(column, t)}
            </DropdownMenuCheckboxItem>
          )
        })}
      </DropdownMenuContent>
    </DropdownMenu>
    <StartChatWithCS
      customTrigger={<div className="w-16 h-8">
        <TooltipWrapper
          trigger={
            <Button size={"icon"} className="w-full h-full border border-seekers-text-lighter" variant={"outline"}><QuestionMarkIcon className="!h-3 !w-3" /></Button>
          }
          content={<p className="text-seekers-primary">{t('misc.chatCustomerSupport')}</p>}
        />
      </div>
      }
    />
  </div>
}

