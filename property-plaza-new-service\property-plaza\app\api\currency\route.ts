import { NextResponse } from "next/server"

const endPoint = process.env.NEXT_PUBLIC_CURRENCY_API! + `latest?apikey=${process.env.CURRENCY_API_KEY}`
export async function GET(req:Request){
  try{
    const currencyConversions = await fetch(endPoint + "&currencies=EUR%2CUSD%2CCAD%2CIDR%2CGBP%2CAUD&base_currency=IDR",{
      next: {
        revalidate: 3600
      }
    }).then(data => data.json())
    return NextResponse.json({
      data: currencyConversions
    },
    {status: 200}
  ) 

  }catch(e){
    return NextResponse.json({
      error: "Failed get currency data"
    },
    {status:500}
  )
  }
}