"use client"
import { useSearchParams } from 'next/navigation'
import ChangePasswordForm from './form/change-password.form'
import EmailForm from '../(auth)/form/email.form'
export default function Content() {
  const searchParams = useSearchParams()
  const email = searchParams.get("email")
  const token = searchParams.get("token")
  if (email && token) {
    return <ChangePasswordForm email={email} token={token} />
  } else {
    return <EmailForm />
  }
}