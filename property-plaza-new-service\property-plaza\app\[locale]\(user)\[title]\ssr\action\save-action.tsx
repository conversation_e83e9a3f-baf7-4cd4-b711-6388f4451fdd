"use client"
import { But<PERSON> } from "@/components/ui/button"
import { useFavoriteListing } from "@/hooks/use-post-favorite-listing"
import { HeartIcon } from "lucide-react"
import { useTranslations } from "next-intl"
import useImageGallery from "../utils/use-image-gallery"
import { cn } from "@/lib/utils"
import { useUserStore } from "@/stores/user.store"
import { useToast } from "@/hooks/use-toast"
import Link from "next/link"
import { noLoginPlanUrl, plansUrl } from "@/lib/constanta/route"

export default function SaveAction({ propertyId, isFavorited }: { propertyId: string, isFavorited?: boolean }) {
  const t = useTranslations("seeker")
  const { favorite, handleFavorite, authenticated: isAuthenticated } = useFavoriteListing(propertyId, isFavorited)
  const { seekers } = useUserStore()
  const { handleOpenAuthDialog } = useImageGallery()
  const { toast } = useToast()
  const handleSetFavorite = () => {
    if (!isAuthenticated) return handleOpenAuthDialog()
    if (seekers.accounts.membership === "Free") {
      toast({
        title: t("misc.subscibePropgram.favorite.title"),
        description: <>
          {t('misc.subscibePropgram.favorite.description')}
          <Button asChild variant={"link"} size={"sm"} className="p-0 text-seekers-primary h-fit w-fit underline">
            <Link href={seekers.email ? plansUrl : noLoginPlanUrl}>{t('cta.subscribe')}</Link>
          </Button>
        </>
      })
      return
    }
    handleFavorite()
  }
  return <>

    <Button
      variant={"ghost"}
      onClick={handleSetFavorite}
      className="md:hidden shadow-none rounded-full text-seekers-text border-seekers-text-lighter w-6 h-6"
      size={"icon"}>
      <HeartIcon className={cn(favorite ? "text-red-500" : "", "!w-4 !h-4")} fill={favorite ? "red" : "#********"} fillOpacity={favorite ? 1 : 0.5} />
    </Button>
    <Button
      variant={"outline"}
      className="max-md:hidden shadow-none rounded-full text-seekers-text border-seekers-text-lighter px-3 py-2 w-fit h-fit"
      size={"sm"}
      onClick={handleSetFavorite}
    >
      <HeartIcon fill={favorite ? "red" : "#********"} className={cn(favorite ? "text-red-500" : "")} fillOpacity={favorite ? 1 : 0.5} />
      {favorite ? t("cta.saved") : t('cta.save')}
    </Button>
  </>
}