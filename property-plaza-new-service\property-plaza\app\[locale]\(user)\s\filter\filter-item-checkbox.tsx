import { cn } from "@/lib/utils";
import { ComponentProps } from "react";

interface FilterItemCheckboxProps extends ComponentProps<"div"> {
  title: string,
  icon: React.ReactNode,
  isActive?: boolean
}
export default function FilterItemCheckbox({ title, icon, isActive, ...rest }: FilterItemCheckboxProps) {
  return <div className={cn("inline-flex gap-1`", rest.className)} {...rest}>
    {icon}{title}
  </div>
}