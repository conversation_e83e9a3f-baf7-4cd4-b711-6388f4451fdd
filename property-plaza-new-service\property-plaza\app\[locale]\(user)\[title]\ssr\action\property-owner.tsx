import { cn } from "@/lib/utils"
import { User } from "lucide-react"
import { useTranslations } from "next-intl"
import Image from "next/image"

type User = { name: string, image?: string }
export default function PropertyOwner({ owner, middleman }: { owner: User, middleman?: User }) {
  const t = useTranslations("seeker")
  return <div className="flex gap-3 items-center">
    <div className="relative w-14 h-14 max-md:w-10 max-md:h-10">
      <div className={
        cn(
          "w-14 h-14 max-md:w-10 max-md:h-10 absolute rounded-full border bg-seekers-text-lighter text-white flex items-center justify-center overflow-hidden",
          middleman && "-top-1.5 -left-1.5 scale-95")}>
        {owner.image ?
          <Image
            src={owner.image || ""} alt={owner.name || t("misc.ownerProperty")} fill
            style={{ objectFit: "cover" }}
          />
          : <User />
        }
      </div>
      {middleman && <div className="w-14 max-md:w-10 h-14 max-md:h-10 relative rounded-full border-2 border-seekers-background bg-seekers-text-lighter text-white flex items-center justify-center overflow-hidden">
        {middleman.image ?
          <Image
            src={middleman.image || ""} alt={owner.name || t("misc.ownerProperty")} fill
            style={{ objectFit: "cover" }}
          />
          : <User />
        }
      </div>}
    </div>
    <div>
      <p className="text-base font-semibold max-w-sm line-clamp-1 capitalize">{middleman ? middleman.name || t("misc.middlemanProperty") : (owner.name || t("misc.ownerProperty"))}</p>
      {middleman &&
        <p className="text-xs text-seekers-text-light">{t('misc.officiallyRepresenting')} {owner.name || t("misc.ownerProperty")}</p>
      }
    </div>
  </div>
}