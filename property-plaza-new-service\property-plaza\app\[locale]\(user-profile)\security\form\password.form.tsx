import { useTranslations } from "next-intl";
import usePasswordFormSchema from "./password-form.schema";
import { useUserStore } from "@/stores/user.store";
import { Form } from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { z } from "zod";
import PasswordInput from "@/components/input-form/password-input";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";

export default function PasswordForm({ onSubmit, isLoading }: { onSubmit: (val: string) => void, isLoading: boolean }) {
  const t = useTranslations("seeker")
  const formSchema = usePasswordFormSchema()
  const { seekers } = useUserStore()
  type typeFormSchema = z.infer<typeof formSchema>
  const form = useForm<typeFormSchema>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      password: ""
    }
  })
  const submitHandler = (val: z.infer<typeof formSchema>) => {
    onSubmit(val.password)
  }
  return <Form {...form}>
    <form onSubmit={form.handleSubmit(submitHandler)} className="space-y-4">
      <PasswordInput
        label={t("form.label.password")}
        form={form}
        name={"password"}
        placeholder=""
      />
      <Button className="w-full" variant={"default-seekers"} loading={isLoading}>
        {t("cta.get2FACode")}
      </Button>
    </form>
  </Form>
}