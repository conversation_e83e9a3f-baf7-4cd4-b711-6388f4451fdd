import { useState } from "react";
import DialogWrapper from "../dialog-wrapper/dialog-wrapper";
import { Check, ChevronDown, Search, X } from "lucide-react";
import DialogFooterWrapper from "../dialog-wrapper/dialog-footer.wrapper";
import { But<PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import DialogHeaderWrapper from "../dialog-wrapper/dialog-header-wrapper";
import { useTranslations } from "next-intl";
import useSeekersSearch from "@/hooks/use-seekers-search";
import DialogTitleWrapper from "../dialog-wrapper/dialog-title-wrapper";
import { DialogDescription } from "../ui/dialog";
import { cn } from "@/lib/utils";
import LocationSearchContent from "./location-search/location-search-content";
import ListingCategoryIcon from "@/app/[locale]/(user)/s/listing-category-icon";
import { useDebounce } from "@/hooks/use-debounce";
import { useSeekerSearchStore } from "@/stores/seeker-search.store";
const LOCATION_ID = "location"
const PROPERTY_SEARCH = "property"


export default function SeekerSearchDialog() {
  const t = useTranslations("seeker")
  const { handleSearch } = useSeekersSearch()
  const [open, setOpen] = useState(false)
  const [openDetail, setOpenDetail] = useState<"location" | "category">("location")
  const { query } = useSeekerSearchStore(state => state)

  const { handleSetType, seekersSearch, propertyType, handleSetQuery } = useSeekersSearch()
  const debounceValue = useDebounce(query, 500)

  return <DialogWrapper
    open={open}
    setOpen={setOpen}
    drawerClassName="relative"
    openTrigger={<div className="w-full border h-10 pl-4 pr-1 flex items-center justify-between text-seekers-text-light text-xs rounded-full border-seekers-text-lighter shadow-md">
      <span className="line-clamp-1">
        {t('listing.search.placeholder')}
      </span>
      <Button variant={"default-seekers"} className="rounded-full !h-8 !w-[2.25rem]" size={"icon"}>
        <Search className="!w-4 !h-4" strokeWidth={3} />
      </Button>
    </div>}
  >
    <div className="flex flex-col h-[calc(80vh-24px)] pb-16">
      <div className="flex-shrink-0 bg-white z-10 border-b">
        <DialogHeaderWrapper className="px-0 !text-center">
          <DialogTitleWrapper className="font-semibold p-0">{t('listing.search.title')}</DialogTitleWrapper>
          <DialogDescription>{t('misc.findYourPerfectProperty')}</DialogDescription>
        </DialogHeaderWrapper>

        <div className="px-4 mb-4 relative">
          <Input
            type="text"
            placeholder="Search destinations"
            className="w-full px-3 py-2 !text-sm !h-10"
            value={query}
            onChange={(e) => {
              handleSetQuery(e.target.value)
            }}
            onKeyDown={(e) => {
              e.stopPropagation()
              if (e.key === "Enter") {
                handleSearch()
                setOpen(false)
              }
            }}
          />
          <X className="w-4 h-4 absolute right-7 top-1/2 -translate-y-1/2 text-seekers-text-light" onClick={() => handleSetQuery("")} />
        </div>
      </div>
      <div className="flex-grow overflow-y-auto">
        <div className="p-4 space-y-4">
          {/* Location Search */}
          <div>
            <div className="flex items-center justify-between"
              onClick={() => setOpenDetail("location")}
            >
              <div className="text-[#B88E57] font-medium mb-2">{t('navbar.search.locationTitle')}</div>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"

              >
                <ChevronDown
                  className={cn("h-4 w-4 transition-transform", openDetail == "location" ? "transform rotate-180" : "")}
                />
              </Button>
            </div>
            {
              openDetail == "location" && <LocationSearchContent />
            }
          </div>

          {/* Category Search */}
          <div>
            <div
              className="flex items-center justify-between"
              onClick={() => setOpenDetail("category")}
            >
              <div className="text-[#B88E57] font-medium mb-2">{t('navbar.search.category')}</div>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
              >
                <ChevronDown
                  className={cn("h-4 w-4 transition-transform", openDetail == "category" ? "transform rotate-180" : "")}
                />
              </Button>
            </div>
            {openDetail == "category" && <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              {propertyType.map(item =>
                <div
                  className="
                  border-seekers-text-lighter
                    hover:bg-seekers-background 
                    gap-2 text-xs 
                    text-seekers-text-light 
                    flex flex-col 
                    justify-center
                    items-center
                     p-4
                    relative
                    border
                    rounded-lg
                    "
                  key={item.id}
                  onClick={(e) => {
                    e.preventDefault()
                    e.stopPropagation()
                    handleSetType(item.value)
                  }}
                >
                  <div className={cn(
                    "absolute top-3 left-3 rounded-full w-4 h-4 flex items-center justify-center",
                    seekersSearch.propertyType.includes(item.value) ? "bg-seekers-primary" : "border border-seekers-text-lighter"
                  )}>
                    <Check className={cn(seekersSearch.propertyType.includes(item.value) ? "w-3 h-3 text-white" : "hidden")} />
                  </div>
                  <ListingCategoryIcon category={item.value} className="!w-6 !h-6" />
                  <span className="text-center">
                    {item.content}
                  </span>
                </div>)}
            </div>
            }
          </div>
        </div>
      </div>
    </div>
    <DialogFooterWrapper className="absolute bottom-0 w-[calc(100%-32px)]">
      <div className="flex justify-between items-center gap-4">
        <Button variant={"link"} className="px-0 text-seekers-primary" onClick={() => setOpen(false)}>{t('cta.clearAll')}</Button>
        <Button className="flex gap-2" variant={"default-seekers"} onClick={() => {
          handleSearch()
          setOpen(false)
        }}>
          <Search />
          {t('cta.search')}
        </Button>
      </div>
    </DialogFooterWrapper>
  </DialogWrapper>
}