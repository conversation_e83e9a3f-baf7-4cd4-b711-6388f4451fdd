import { PASSWORD_MIN_LENGTH } from "@/lib/constanta/constant";
import { useTranslations } from "next-intl";
import { z } from "zod";
import { PasswordPattern } from "../../(auth)/form/use-sign-up-form.schema";


export default function useChangePasswordFormSchema(){
  const t = useTranslations("universal")
  const formSchema = z.object({
    password: z.string().min(1, {
      message: t("form.utility.fieldRequired", { field: t("form.field.password") })
    }).min(PASSWORD_MIN_LENGTH, { message: t("form.utility.minimumLength", { length: PASSWORD_MIN_LENGTH, field: t("form.field.password") }) })    
    .refine(val => PasswordPattern.test(val),{
      message: t("form.utility.passwordWeak")
    }),
    confirmPassword: z.string().min(1, {
      message: t("form.utility.fieldRequired", { field: t("form.field.confirmPassword") })
    })
  }).refine(data => {
    return data.password == data.confirmPassword
  }, {
    message: t("form.utility.fieldNotMatch", { field: `${t("form.field.password")} ${t("conjuntion.and")} ${t("form.field.confirmPassword")}` }),
    path: ["confirmPassword"]
  })
  return formSchema
}