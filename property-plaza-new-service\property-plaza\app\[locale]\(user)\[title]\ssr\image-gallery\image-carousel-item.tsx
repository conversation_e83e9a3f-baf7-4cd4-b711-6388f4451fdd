"use client"

import { CarouselItem } from "@/components/ui/carousel"
import { useFavoriteListing } from "@/hooks/use-post-favorite-listing"
import { imagePlaceholder } from "@/lib/constanta/image-placeholder"
import Image from "next/image"
import useImageGallery, { IMAGE_DETAIL_BUTTON_ID } from "../utils/use-image-gallery"

export default function ImageCarouselItem({ imageUrl, index, isPriorityImage }: { imageUrl: string, index: number, isPriorityImage?: boolean }) {
  const { authenticated: isAuthenticated } = useFavoriteListing("")
  const { handleShareActiveImageCarousel, handleOpenAuthDialog } = useImageGallery()
  const handleOpenDetailCarousel = () => {
    const buttonDialogCarousel = window.document.getElementById(IMAGE_DETAIL_BUTTON_ID)
    buttonDialogCarousel?.click()
  }
  return <CarouselItem className="relative"
    onClick={() => {
      if (!isAuthenticated) return handleOpenAuthDialog()
      handleOpenDetailCarousel()
      handleShareActiveImageCarousel(index)
    }}
  >
    <div className="absolute inset-0 z-10 pointer-events-none watermark-overlay" />
    <Image
      src={imageUrl}
      alt=""
      fill
      sizes="300px"
      priority={isPriorityImage}
      loading={isPriorityImage ? undefined : "lazy"}
      blurDataURL={imagePlaceholder}
      placeholder="blur"
      style={{ objectFit: 'cover' }}
    />
  </CarouselItem>
}