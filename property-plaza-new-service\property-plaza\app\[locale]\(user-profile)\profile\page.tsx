import MainContentLayout from "@/components/seekers-content-layout/main-content-layout";
import ProfileBreadCrumb from "./bread-crumb";
import { getLocale, getTranslations } from "next-intl/server";
import { Metadata } from "next";
import { useTranslations } from "next-intl";
import ProfilePictureForm from "./form/profile-picture.form";
import ProfileForm from "./form/profile.form";
import { profileUrl } from "@/lib/constanta/route";
import { routing } from "@/lib/locale/routing";

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations("seeker")
  const locale = await getLocale() || routing.defaultLocale
  const baseUrl = process.env.USER_DOMAIN || "https://www.property-plaza.com/"
  return {
    title: t("metadata.profile.title"),
    description: t("metadata.profile.description"),
    alternates: {
      canonical: baseUrl + locale + profileUrl,
      languages: {
        en: baseUrl + "en" + profileUrl,
        id: baseUrl + "id" + profileUrl,
        "x-default": baseUrl + profileUrl.replace("/", ""),
      }
    },
    openGraph: {
      title: t("metadata.profile.title"),
      description: t("metadata.profile.description"),
      images: [
        {
          url: baseUrl + "og.png",
          width: 1200,
          height: 630,
          alt: "Property Plaza",
        }
      ],
      type: "website",
      url: baseUrl + locale + profileUrl,
      countryName: "Indonesia",
      emails: "<EMAIL>",
      locale: locale,
      alternateLocale: routing.locales,
      siteName: "Property plaza"
    },
    applicationName: "Property plaza",
    twitter: {
      card: "summary_large_image",
      title: t("metadata.profile.title"),
      description: t("metadata.profile.description"),
      images: [baseUrl + "og.jpg"],
    },

    robots: {
      index: false,
      follow: false,
      nocache: false,
    },
  }
}





export default function ProfilePage() {
  const t = useTranslations("seeker")
  return <>
    <ProfileBreadCrumb />
    <MainContentLayout className="space-y-8 my-8 max-sm:px-0">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">{t("setting.profile.personalInfo.title")}</h1>
        <h2 className="text-muted-foreground mt-2">{t("settings.profile.personalInfo.description")}</h2>
      </div>
      <ProfilePictureForm />
      <ProfileForm />
    </MainContentLayout>
  </>
}