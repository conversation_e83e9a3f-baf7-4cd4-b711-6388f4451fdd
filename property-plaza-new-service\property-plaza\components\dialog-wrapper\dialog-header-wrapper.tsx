"use client"

import { useMediaQuery } from "@/hooks/use-media-query"
import { DialogHeader } from "../ui/dialog"
import { DrawerHeader } from "../ui/drawer"

export default function DialogHeaderWrapper({ children, className }: { children: React.ReactNode, className?: string }) {
  const isDesktop = useMediaQuery("(min-width:1024px)")
  if (isDesktop) {
    return <DialogHeader className={className}>
      {children}
    </DialogHeader>
  } else {
    return <DrawerHeader className={className}>
      {children}
    </DrawerHeader>
  }
}