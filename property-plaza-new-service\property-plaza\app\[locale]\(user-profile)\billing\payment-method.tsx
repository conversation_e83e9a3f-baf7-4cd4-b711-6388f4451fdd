"use client"
import { Bad<PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { GET_TRANSACTION_SEEKER_QUERY_KEY, useGetTransactionSeekerQuery } from "@/core/applications/queries/transaction/use-get-transaction-seeker-list"
import { CreditCard } from "lucide-react"
import { useTranslations } from "next-intl"
import { useSearchParams } from "next/navigation"
import { useQueryClient } from '@tanstack/react-query';
import { Skeleton } from "@/components/ui/skeleton"
import { PaymentMethod } from "@/core/domain/transaction/transaction"
import { useUpdatePaymentMethod } from "@/core/applications/mutations/transaction/use-update-payment-method"
import { useToast } from "@/hooks/use-toast"
import PaymentItem from "./payment-item"

// Inside your component
const billingInfo = {
  companyName: "Voor<PERSON>ad Fanaat B.V.",
  address: "De Nieuwe Erven 3",
  postalCode: "5431NV",
  city: "Cuijk",
  country: "Netherlands",
  vatNumber: "NL123456789B01",
}

export default function PaymentMethodContent({ paymentMethod }: { paymentMethod: PaymentMethod[] }) {
  const t = useTranslations("seeker")
  const params = useSearchParams()
  const page = +(params.get("page") || 1)
  const per_page = +(params.get("per_page") || 10)
  const start_date = params.get("start_date") || ""
  const end_date = params.get("end_date") || ""
  const type = params.get("type") as string
  const transactionQuery = useGetTransactionSeekerQuery({
    page,
    per_page,
    search: "",
    type,
    start_date: start_date,
    end_date: end_date
  })
  return <Card>
    <CardHeader>
      <CardTitle className="text-seekers-primary">{t("setting.subscriptionStatus.billing.paymentMethod.title")}</CardTitle>
      <CardDescription>{t("setting.subscriptionStatus.billing.paymentMethod.description")}</CardDescription>
    </CardHeader>
    <CardContent>
      <div className="space-y-6">
        {/* Payment Method */}
        {
          paymentMethod.length == 0 ? <>
            <div className="flex flex-col items-center text-center w-full">
              <div className="border rounded-xl p-2 w-fit ">
                <CreditCard className="h-6 w-6 text-seekers-primary" />
              </div>
              <p className="text-seekers-text-light">{t('info.noPaymentMethodsAdded')}</p>
            </div>
          </> :
            <>
              <div className="p-4 border border-seekers-primary/20 rounded-lg">
                {paymentMethod.map(item => <PaymentItem key={item.id} item={item} />
                )}
              </div>
            </>
        }
        <Button
          variant="outline"
          className="w-full mt-4 border-seekers-primary text-seekers-primary hover:bg-[#FAF6F0] hover:text-seekers-primary"
        >
          {t("cta.addPaymentMethod")}
        </Button>

        {/* Billing Information */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-seekers-primary">{t("setting.subscriptionStatus.billing.billingInformation.title")}</h3>
            <Button variant="ghost" size="sm" className="text-seekers-primary hover:text-seekers-primary hover:bg-[#FAF6F0]">
              {t("cta.editBilling")}
            </Button>
          </div>
          <div className="space-y-1 text-sm p-4 border border-seekers-primary/20 rounded-lg">
            {transactionQuery.isLoading ? <>
              {[0, 1, 2, 3].map(item => <Skeleton key={item} className="w-full md:w-1/2 h-8" />)}
            </> : <>
              <p className="font-medium">{transactionQuery.data?.data?.metadata.name}</p>
              <p>{transactionQuery.data?.data?.metadata.addressOne}</p>
              <p>{transactionQuery.data?.data?.metadata.addressTwo}</p>
              <p>
                {transactionQuery.data?.data?.metadata.postalCode} {transactionQuery.data?.data?.metadata.city}
              </p>
              <p>{transactionQuery.data?.data?.metadata.country}</p>
            </>}
          </div>
        </div>
      </div>
    </CardContent>
  </Card>
}