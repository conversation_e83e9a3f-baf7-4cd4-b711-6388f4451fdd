import ChatBubble from "@/components/chat-messages/chat-bubble";
import { Badge } from "@/components/ui/badge";
import { MessageText } from "@/core/domain/messages/messages";
import { useUserStore } from "@/stores/user.store";
import moment from "moment";
import { useTranslations } from "next-intl";
import React from "react";

export default function ChatDetailMessages({ messages, chatEnded }: { messages: MessageText[], chatEnded?: boolean }) {
  const t = useTranslations("seeker")
  let lastDate: string = ""
  const { code } = useUserStore(state => state.seekers)
  return <>
    {messages.map((item, idx) => {
      const currentDate = item.createdAt
      const today = moment()

      // Compare the dates directly within the loop
      const showDateBadge = lastDate === "" || moment(lastDate).isBefore(moment(currentDate), "day");
      const formattedDate = moment(currentDate).isSame(today, "day") ? t('misc.today') : moment(currentDate).format("DD-MMM-YY")

      // Update the lastDate for the next iteration
      lastDate = currentDate;
      return <React.Fragment key={idx}>
        {
          showDateBadge && <div className="w-full flex justify-center sticky top-0">
            <Badge variant={"outline"} className="border-neutral-300 text-neutral-500 bg-seekers-primary-lighter min-w-[84px] text-center flex items-center justify-center">{formattedDate}</Badge>
          </div>
        }
        <div className="w-full space-y-2 my-2">
          <ChatBubble  {...item} code={code} isSeeker />
        </div>
      </React.Fragment>
    }
    )}
    {chatEnded && <ChatBubble
      code="000"
      text={t('message.textChatEnded')}
      createdAt={messages[messages.length - 1]?.createdAt || moment().format("DD-MM-YYYY")}
      displayAs=""
      displayName=""
      id="000"
      isRead
      isSent
      status=""
    >
    </ChatBubble>}
  </>
}
