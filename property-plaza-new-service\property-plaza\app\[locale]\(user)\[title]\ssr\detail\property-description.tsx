"use client"

import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { useTranslations } from "next-intl"
import { useEffect, useRef, useState } from "react"

export default function PropertyDescription({ description }: { description: string }) {
  const t = useTranslations("seeker")
  const [isOpen, setIsOpen] = useState(false)
  const [needsClamp, setNeedsClamp] = useState(false)
  const textRef = useRef<HTMLParagraphElement>(null)

  useEffect(() => {
    const checkHeight = () => {
      const element = textRef.current
      if (!element) return

      // Get the line height and calculate height of 3 lines
      const style = window.getComputedStyle(element)
      const lineHeight = parseInt(style.lineHeight)
      const maxHeight = lineHeight * 10

      // Compare with the actual scroll height
      setNeedsClamp(element.scrollHeight > maxHeight)
    }

    checkHeight()

    // Re-check on window resize
    window.addEventListener('resize', checkHeight)
    return () => window.removeEventListener('resize', checkHeight)
  }, [description])

  return <>
    <p ref={textRef} className={cn("text-seekers-text whitespace-pre-wrap", isOpen ? "line-clamp-none" : "line-clamp-[10]")} style={{ lineClamp: isOpen ? "none" : 3 }}>{description}</p>
    {needsClamp && (
      <Button
        variant="link"
        className="text-seekers-text p-0 w-fit h-fit"
        onClick={() => setIsOpen(prev => !prev)}
      >
        {!isOpen ? t('cta.readMore') : t('cta.readLess')}
      </Button>
    )}
  </>
}