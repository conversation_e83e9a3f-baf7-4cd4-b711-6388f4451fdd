import { cn } from "@/lib/utils";
import { ComponentProps } from "react";

interface FilterContentLayout extends ComponentProps<"div"> {
  children: React.ReactNode,
  title?: string,
  description?: string,
  titleClassName?: string
}
export default function FilterContentLayout({ children, title, description, titleClassName, ...rest }: FilterContentLayout) {
  return <div className={cn("space-y-6 relative", rest.className)} {...rest}>
    <div className="space-y-2 text-seekers-text">
      <h3 className={cn("font-bold text-lg", titleClassName)}>{title}</h3>
      <p className="text-xs">{description}</p>
    </div>
    {children}
  </div>
}