import { useTranslations } from "next-intl";
import FilterContentLayout from "./filter-content-layout";
import { BaseSelectInputValue } from "@/types/base";
import { Trash2 } from "lucide-react";
import CheckboxFilterItem from "./checkbox-filter-item";
import { useSeekerFilterStore } from "@/stores/seekers-filter-2.store";
import { FaStreetView } from "react-icons/fa";
import Image from "next/image";
import MainStreetIcon from "@/components/icons/property-detail/Mainstreet.svg"
import CloseToBeachIcon from "@/components/icons/property-detail/Close to beach.svg"
import { cn } from "@/lib/utils";
export default function LocationFilter() {
  const t = useTranslations("seeker")
  const { location, setLocation } = useSeekerFilterStore(state => state)
  const views: BaseSelectInputValue<string>[] = [
    {
      id: "1",
      content: <div className="flex gap-1 items-center">
        <Image
          src={MainStreetIcon} alt="main-street"
          className={cn("w-4 h-4 invert", location.includes("MAIN_STREET") ? "invert" : "invert-0")}
          width={16}
          height={16}
        />
        <span className="">{t('listing.locationFilter.optionOne.title')}</span>
      </div>,
      value: "MAIN_STREET"
    },
    {
      id: "2",
      content: <div className="flex gap-1 items-center">
        <Image
          src={CloseToBeachIcon}
          alt="close-to-beach"
          className={cn("w-4 h-4 ", location.includes("CLOSE_TO_BEACH") ? "invert" : "invert-0")}
          width={16}
          height={16}
        />
        <span className="">{t('listing.locationFilter.optionTwo.title')}</span>
      </div>,
      value: "CLOSE_TO_BEACH"
    }
  ]
  return <FilterContentLayout title={t('listing.locationFilter.title')}>
    <div className="flex flex-wrap gap-2">
      {
        views.map(item =>
          <CheckboxFilterItem
            key={item.id}
            item={item}
            setValue={setLocation}
            isActive={location.includes(item.value)} />
        )}
    </div>
  </FilterContentLayout>
}