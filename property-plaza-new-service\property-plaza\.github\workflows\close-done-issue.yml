name: Close Issue on Done Status
on:
  issues:
    types: [edited]

permissions:
  issues: write
  repository-projects: read

jobs:
  close_issue:
    runs-on: ubuntu-latest
    steps:
      - name: Check if issue status changed to Done
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const issue = context.payload.issue;
            
            // Get the project fields for this issue
            const query = `query($owner:String!, $repo:String!, $number:Int!) {
              repository(owner:$owner, name:$repo) {
                issue(number:$number) {
                  projectItems(first: 1) {
                    nodes {
                      status: fieldValueByName(name: "Status") {
                        ... on ProjectV2ItemFieldSingleSelectValue {
                          name
                        }
                      }
                    }
                  }
                }
              }
            }`;
            
            const variables = {
              owner: context.repo.owner,
              repo: context.repo.repo,
              number: issue.number
            };
            
            const result = await github.graphql(query, variables);
            const projectItem = result.repository.issue.projectItems.nodes[0];
            
            if (projectItem && projectItem.status && projectItem.status.name === "Done") {
              await github.rest.issues.update({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: issue.number,
                state: "closed"
              });
              console.log(`Closed issue #${issue.number} as it is marked as 'Done'`);
            } else {
              console.log("Issue status is not 'Done', skipping.");
            }
