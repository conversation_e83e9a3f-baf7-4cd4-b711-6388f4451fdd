"use client"
import moment from 'moment';
import { useTranslations } from "next-intl";
import { useState } from "react";
import { But<PERSON> } from "../ui/button";
import { MessageText } from "@/core/domain/messages/messages";
import useTranslateMessage from "@/core/applications/mutations/translation/use-translate-message";
import { useToast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion"

interface ChatBubbleProps extends MessageText {
  isSeeker?: boolean

}

export default function ChatBubble({
  createdAt, displayAs, isRead, isSent, text, code, isSeeker }: ChatBubbleProps) {
  const t = useTranslations("component")
  const { toast } = useToast()
  const [isTranslated, setIsTranslated] = useState(false)
  const [translatedText, setTranslatedText] = useState<string | null>(null)
  const translationMutation = useTranslateMessage()
  const isSender = displayAs == code
  const chatPosition = isSender ? "items-end" : "items-start"
  const handleTranslation = async () => {
    if (translatedText !== null) {
      setIsTranslated(prev => !prev)
      return
    }
    try {
      const data = await translationMutation.mutateAsync(text)
      setIsTranslated(prev => !prev)
      const translatedResult = data.data.translatedText.translatedText as string
      setTranslatedText(translatedResult)
      // const translate = await translationMutation.mutateAsync(text)
    } catch (e: any) {
      toast({
        title: "there's issue with translations"
      })
    }
  }
  return <motion.div
    initial={{ opacity: 0, translateY: 20, }}
    animate={{ opacity: 1, translateY: 0, }}
    transition={{
      duration: 0.2,
      ease: "easeInOut"
    }} className={`w-full flex flex-col ${chatPosition} gap-2`}>
    <div className={cn("text-sm p-4 rounded-sm bg-background max-w-[256px] md:max-w-[348px]", isSeeker ? "bg-seekers-primary/5" : "")}>
      <div>
        {isTranslated ? translatedText || text : text}
      </div>
      {!isSender &&
        <Button variant={"ghost"}
          className="h-fit w-fit px-0 text-neutral-light font-normal"
          size={"sm"}
          onClick={() => handleTranslation()}>
          {isTranslated ? t('cta.seeOriginal') : t('cta.seeTranslation')}
        </Button>
      }
    </div>
    <div className={`flex gap-1 ${chatPosition} text-xs text-neutral`}>
      {/* {!isSender ? <></> : isRead ? <CheckCheck className="text-green-500 w-3 h-3" /> :
        isSent ? <CheckCheck className="w-3 h-3 text-neutral-500" /> : <Check className="w-3 h-3 text-neutral-500" />} */}
      <p>{moment(createdAt).format("HH:mm")}</p>
    </div>
  </motion.div>
}
