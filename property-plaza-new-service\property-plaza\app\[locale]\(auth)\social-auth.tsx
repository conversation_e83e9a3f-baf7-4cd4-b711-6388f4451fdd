"use client"
import { useEffect } from "react";
import Cookies from "js-cookie";
import { ACCESS_TOKEN } from "@/lib/constanta/constant";
import useSearchParamWrapper from "@/hooks/use-search-param-wrapper";

// Function to get auth redirect data
export default function SocialAuthFormatter({ accessToken, status, expired }: { accessToken?: string, status?: string, expired?: string }) {
  const { removeQueryParam } = useSearchParamWrapper()
  useEffect(() => {
    if (!accessToken || status !== "200") return
    Cookies.set(ACCESS_TOKEN, accessToken, { expires: +(expired || 1440) / 1440 })
    removeQueryParam(["request", "at", "status_code", "provider", "ma", "message"], true)
  }, [accessToken, status, removeQueryParam, expired])

  return <></>
}