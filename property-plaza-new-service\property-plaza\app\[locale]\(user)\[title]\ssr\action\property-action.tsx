import { ListingContractType } from "@/core/domain/listing/listing";
import MobilePropertyAction from "./mobile-property-action";
import { ItemWithSuffix } from "@/core/domain/utils/utils";
import DesktopPropertyAction from "./desktop-property-action";
import { cookies } from "next/headers";
import { SeekersSettings } from "@/stores/seekers-settings.store";

export default async function PropertyAction({ price, type, minDuration, maxDuration, propertyId, owner, isFavorited, isNegotiable, middleman, availableAt, isActiveListing = true, chatCount = 0 }: {
  price: number,
  type: ListingContractType
  minDuration: ItemWithSuffix,
  maxDuration: ItemWithSuffix,
  availableAt: string | null
  propertyId: string,
  owner?: {
    ownerId: string,
    ownerName: string,
    ownerProfileUrl: string
  },
  middleman?: {
    middlemanId: string,
    middlemanName: string,
    middlemanProfileUrl: string
  }
  isFavorited?: boolean,
  chatCount?: number,
  isNegotiable?: boolean,
  isActiveListing?: boolean
}) {
  const cookiesStore = cookies()

  const setting: string | undefined = cookiesStore.get("seekers-settings")?.value
  const currency: SeekersSettings | undefined = setting ? JSON.parse(setting)?.state : undefined
  const locale: string | undefined = cookiesStore.get('NEXT_LOCALE')?.value
  return <>
    {/* Mobile-device */}
    <div className="md:hidden fixed bottom-0 left-0 bg-white p-4 w-full flex justify-between">
      <MobilePropertyAction
        maxDuration={maxDuration}
        minDuration={minDuration}
        ownerId={owner?.ownerId || ""}
        price={price}
        propertyId={propertyId}
        type={type}
        currency={currency?.currency || "EUR"}
        isFavorited={isFavorited}
        isNegotiable={isNegotiable}
        isActiveListing={isActiveListing}
        middlemanId={middleman?.middlemanId}
        availableAt={availableAt}
        owner={owner}
        middleman={middleman}

      />
    </div>
    {/* Desktop-device */}
    <div className="max-sm:hidden w-full space-y-6 sticky top-[200px]">
      <DesktopPropertyAction
        maxDuration={maxDuration}
        minDuration={minDuration}
        availableAt={availableAt}
        owner={owner}
        price={price}
        propertyId={propertyId}
        type={type}
        isFavorited={isFavorited}
        currency={currency?.currency || "EUR"}
        chatCount={chatCount}
        isNegotiable={isNegotiable}
        isActiveListing={isActiveListing}
        middleman={middleman}
      />
    </div>
  </>
}