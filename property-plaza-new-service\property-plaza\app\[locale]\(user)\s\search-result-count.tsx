"use client"
import { Skeleton } from "@/components/ui/skeleton"
import { useSeekerFilterResultStore } from "@/stores/seeker-filter-result.store"
import { useSeekerSearchStore } from "@/stores/seeker-search.store"
import { useSeekersSearchMapUtil } from "@/stores/seekers-search-map-utils"
import { useTranslations } from "next-intl"

export default function SearchResultCount() {
  const { highlightedListing } = useSeekersSearchMapUtil()
  const { total, isLoading } = useSeekerFilterResultStore()
  const t = useTranslations("seeker")
  return <h3 className="font-semibold max-sm:text-xs text-xl text-seekers-text">
    {isLoading ? <><Skeleton className="w-40 h-6" /></> :

      total == 0 ? t("listing.misc.searchNoResultCount") :
        t('listing.misc.searchResultCount', { count: total })
    }
  </h3>
}