name: Assign Issue on Ready Status
on:
  issues:
    types: [edited]

permissions:
  issues: write
  repository-projects: read

jobs:
  assign_issue:
    runs-on: ubuntu-latest
    steps:
      - name: Check if issue status changed to Ready
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const issue = context.payload.issue;
            
            // Get the project fields for this issue
            const query = `query($owner:String!, $repo:String!, $number:Int!) {
              repository(owner:$owner, name:$repo) {
                issue(number:$number) {
                  projectItems(first: 1) {
                    nodes {
                      status: fieldValueByName(name: "Status") {
                        ... on ProjectV2ItemFieldSingleSelectValue {
                          name
                        }
                      }
                    }
                  }
                }
              }
            }`;
            
            const variables = {
              owner: context.repo.owner,
              repo: context.repo.repo,
              number: issue.number
            };
            
            const result = await github.graphql(query, variables);
            const projectItem = result.repository.issue.projectItems.nodes[0];
            
            if (projectItem && projectItem.status && projectItem.status.name === "Ready") {
              const assignee = "rizkiilma21";
              if (!issue.assignee) {
                await github.rest.issues.addAssignees({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  issue_number: issue.number,
                  assignees: [assignee]
                });
                console.log(`Assigned issue #${issue.number} to ${assignee}`);
              } else {
                console.log(`Issue #${issue.number} is already assigned to ${issue.assignee.login}`);
              }
            } else {
              console.log("Issue status is not 'Ready', skipping.");
            }
