"use client"
import TooltipWrapper from "@/components/tooltop-wrapper/tooltip-wrapper"
import useSeekersSearch from "@/hooks/use-seekers-search"
import { useTranslations } from "next-intl"

export default function PropertyFormatter({ value, textOnly = false }: { value: string, textOnly?: boolean }) {
  const t = useTranslations("seeker")
  const { propertyTypeFormatHelper } = useSeekersSearch()
  const propertyType = propertyTypeFormatHelper(value.split(","))
  if (value.includes("all")) {
    if (textOnly) {
      return t('listing.filter.category.all.title')
    } else {
      return <p>{t('listing.filter.category.all.title')}</p>
    }
  }
  if (textOnly) {
    return propertyType.toString().replace(",", ` ${t('conjuntion.and')} `)
  }
  return <>
    {propertyType.length > 2 ?
      <TooltipWrapper
        trigger={
          <div>
            <p>{propertyType[0]} {t('conjuntion.and')} <span>+ {propertyType.length - 1} {t('misc.more')}</span></p>
          </div>
        }
        content={
          propertyType.toString().replaceAll(",", ", ")
        }
        contentClassName="text-seekers-text"
      >

      </TooltipWrapper>
      :
      <div>
        <p >{propertyType.toString().replace(",", ` ${t('conjuntion.and')} `)}</p>
      </div>
    }
  </>
}

