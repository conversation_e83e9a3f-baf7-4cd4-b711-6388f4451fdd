import { FormDescription } from "@/components/ui/form"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger } from "@/components/ui/select"
import { cn } from "@/lib/utils"
import { BaseSelectInputValue } from "@/types/base"
import { useTranslations } from "next-intl"
import { ComponentProps } from "react"

interface SelectFilterProps extends ComponentProps<"select"> {
  value: string,
  setValue: (val: string) => void,
  options: BaseSelectInputValue<any>[],
  title: string,
  placeholder?: string,
  description?: string
}
export default function SelectFilter({ title, description, placeholder, options, setValue, value, ...rest }: SelectFilterProps) {
  const valueFormatter = () => {
    const selectedOption = options.find(item => item.value == value)
    return selectedOption?.content
  }
  return <div className="space-y-1">
    <Label>{title}</Label>
    <Select value={value} onValueChange={val => {
      setValue(val)
    }} defaultValue="ANY" disabled={rest.disabled}>
      <SelectTrigger>
        {value ? valueFormatter() : placeholder}
      </SelectTrigger>
      <SelectContent>
        {
          options.map(item =>
            <SelectItem key={item.id} value={item.value}>{item.content}</SelectItem>
          )
        }
      </SelectContent>
    </Select>
    <p
      className={"text-[0.8rem] text-muted-foreground"}
    >
      {description}
    </p>
  </div>
}