"use client"
import DialogFooterWrapper from "@/components/dialog-wrapper/dialog-footer.wrapper";
import DialogHeaderWrapper from "@/components/dialog-wrapper/dialog-header-wrapper";
import DialogTitleWrapper from "@/components/dialog-wrapper/dialog-title-wrapper";
import DialogWrapper from "@/components/dialog-wrapper/dialog-wrapper";
import { Button } from "@/components/ui/button";
import { Search, Settings2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";
import TypeProperty from "./type-property";
import ViewFilter from "./view";
import RentalIncludingFilter from "./rental-including";
import LocationFilter from "./location";
import { Separator } from "@/components/ui/separator";
import FeaturesFilter from "./features";
import PropertyConditionFilter from "./property-condition";
import OtherFeatureFilter from "./other-features-filter";
import RoomsAndBeds from "./rooms-and-beds";
import PriceRange from "./price-range";
import PropertySizeFilter from "./property-size";
import SubtypeFilter from "./subtype-filter";
import { useSeekerFilterStore } from "@/stores/seekers-filter-2.store";
import { seekersListingFilterType } from "@/core/domain/listing/listing-seekers";
import MinimumContractDuration from "./minimum-contract-duration";
import YearsOfBuild from "./years-of-build";
import useSeekersFilter from "@/hooks/use-seekers-filter";
import { ScrollArea } from "@/components/ui/scroll-area";

export default function FilterDialog({ conversions }: { conversions: { [key: string]: number } }) {
  const t = useTranslations("seeker")

  const [open, setOpen] = useState<boolean>(false)
  const typeProperty = useSeekerFilterStore(state => state.typeProperty)
  const seekersFilter = useSeekersFilter(conversions)
  return <DialogWrapper
    open={open}
    setOpen={setOpen}
    openTrigger={<Button variant={"outline"} className="border-seekers-text-lighter text-seekers-text bg-[#F0F0F0]">
      <Settings2 className="!w-4 !h-4" />
      <span className="text-xs font-medium">{t('cta.filters')}</span>
    </Button>}
    dialogClassName="!w-fit !max-w-fit"
    drawerClassName="!pb-0"
  >
    <DialogHeaderWrapper>
      <DialogTitleWrapper>
        {t('cta.filters')}
      </DialogTitleWrapper>
    </DialogHeaderWrapper>
    <ScrollArea className="lg:max-w-[820px] space-y-6 md:min-w-[820px] md:h-[480px] xl:h-[640px] lg:max-h-screen lg:overflow-hidden lg:pr-3 pb-8">
      <div className="space-y-6">
        <TypeProperty />
        <SubtypeFilter />
        <Separator />
        <PriceRange conversions={conversions} />
        <PropertySizeFilter />
        <Separator />
        {
          seekersListingFilterType.land !== typeProperty &&
          <>
            <RoomsAndBeds />
            <Separator />
            <RentalIncludingFilter />
          </>
        }


        <LocationFilter />
        {
          seekersListingFilterType.land !== typeProperty &&
          <>
            <FeaturesFilter />
            <PropertyConditionFilter />

            <Separator />
            <OtherFeatureFilter />
          </>
        }
        <Separator />
        <ViewFilter />
        <Separator />
        <MinimumContractDuration />
        {
          seekersListingFilterType.land !== typeProperty &&
          <>
            <Separator />

            <YearsOfBuild />
          </>
        }
      </div>
    </ScrollArea>
    <DialogFooterWrapper className="max-sm:sticky max-sm:bottom-0 bg-white">
      <div className="flex justify-between w-full items-center gap-4 ">
        <Button variant={"link"} className="px-0 text-seekers-primary" onClick={() => {
          seekersFilter.handleClearFilter()
        }}>{t('cta.clearAll')}</Button>

        <Button className="flex gap-2" variant={"default-seekers"} onClick={() => {
          setOpen(false)
          seekersFilter.handleFilter()
        }}>
          <Search />
          {t('cta.search')}
        </Button>
      </div>
    </DialogFooterWrapper>
  </DialogWrapper>
}