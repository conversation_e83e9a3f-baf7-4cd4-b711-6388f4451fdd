name: Milestone Issue Reminder
on:
  schedule:
    - cron: "0 12 * * *" # Runs daily at 12:00 UTC (fixed the cron syntax)
  workflow_dispatch: # Allows manual execution

permissions:
  issues: write # Needed to read issues and create comments
  contents: read # Needed to access repository content

jobs:
  check_milestone_issues:
    runs-on: ubuntu-latest
    steps:
      - name: Check milestone issues and send reminders
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const { owner, repo } = context.repo;
            const now = new Date();
            
            // Get all open issues with a milestone
            const issues = await github.rest.issues.listForRepo({
              owner,
              repo,
              state: "open",
              per_page: 100
            });
            
            for (const issue of issues.data) {
              if (!issue.milestone) continue; // Skip if no milestone
              
              const milestoneDue = new Date(issue.milestone.due_on);
              const timeDiff = (milestoneDue - now) / (1000 * 60 * 60 * 24); // Convert ms to days
              
              if (timeDiff <= 4 && timeDiff > 3) { // Run exactly 4 days before the deadline
                const issueTitle = issue.title.toUpperCase(); // Normalize title for easier comparison
                let mention = "";
                
                // Assign based on keywords in the title
                if (issueTitle.includes("[BACKEND]") || issueTitle.includes("[BO]")) {
                  mention = "@jowy2211"; // Anjas
                } else if (issueTitle.includes("[SEEKERS]") || issueTitle.includes("[SEEKER]") || 
                         issueTitle.includes("[OWNER]") || issueTitle.includes("[OWNERS]")) {
                  mention = "@Zuuper"; // Adit
                } else if (issue.labels.some(label => label.name.toLowerCase() === "ready")) {
                  mention = "@rizkiilma21"; // Rizki if "Ready" label is present
                }
                
                // Create comment if a relevant status is found
                if (mention) {
                  await github.rest.issues.createComment({
                    owner,
                    repo,
                    issue_number: issue.number,
                    body: `⏳ Reminder: This issue is still open and the milestone deadline is approaching. ${mention}, please check this issue.`
                  });
                  console.log(`Reminder sent for issue #${issue.number}`);
                } else {
                  console.log(`No relevant status found for issue #${issue.number}, skipping.`);
                }
              }
            }
