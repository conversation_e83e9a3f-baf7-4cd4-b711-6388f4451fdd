@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 40% 98%;
    --foreground: 180 21% 92%;
    --seekers-background: 0 0% 100%;
    --seekers-foreground: 40 68% 97%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --white: 210 40% 98%;
    --black: 229 84% 5%;
    --neutral: 215 16% 47%;
    --neutral-light: 215 20% 65%;
    --neutral-lighter: 214 32% 91%;
    --neutral-lightest: 210 40% 96%;
    --neutral-dark: 215 25% 27%;
    --neutral-darker: 217 33% 17%;
    --neutral-darkest: 222 47% 11%;

    --primary: 178.6 97.8% 17.5%;
    --primary-foreground: 210 40% 98%;
    --primary-light: 179 25.4% 44.7%;
    --primary-lighter: 180 20.6% 74.3%;
    --primary-lightest: 180 21% 92%;
    --primary-dark: 180 100% 12.2%;
    --primar-darker: 178.4 100% 7.3%;

    --seekers-primary: 34 39% 52%;
    --seekers-primary-foreground: 34 16.2% 46.6%;
    --seekers-primary-darkest: 35 37% 20%;
    --seekers-primary-darker: 34 36% 34%;
    --seekers-primary-dark: 34 36% 48%;
    --seekers-primary-light: 34 34% 65%;
    --seekers-primary-lighter: 34 34% 84%;
    --seekers-primary-lightest: 40 33% 95%;

    --secondary: 140 34% 72%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --secondary-light: 135 38% 82%;
    --secondary-lighter: 134 40% 92%;
    --secondary-dark: 130 13% 31%;
    --secondary-darker: 130 13% 31%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;

    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --white: 210 40% 98%;
    --black: 229 84% 5%;
    --neutral: 215 16% 47%;
    --neutral-light: 215 20% 65%;
    --neutral-lighter: 214 32% 91%;
    --neutral-lightest: 210 40% 96%;
    --neutral-dark: 215 25% 27%;
    --neutral-darker: 217 33% 17%;
    --neutral-darkest: 222 47% 11%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --primary-light: 215 20% 65%;
    --primary-lighter: 210 40% 96%;
    --primary-dark: 215 25% 27%;
    --primar-darker: 222 47% 11%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --secondary-light: 222.2 47.4% 11.2%;
    --secondary-lighter: 222.2 47.4% 11.2%;
    --secondary-dark: 222.2 47.4% 11.2%;
    --secondary-darker: 222.2 47.4% 11.2%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  input,
  textarea {
    @apply appearance-none;
    -webkit-appearance: none;
    -moz-appearance: none;
  }
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-neutral-900 text-sm;
  }
  ::-ms-reveal {
    display: none;
  }
}
html {
  scroll-behavior: smooth;
  height: -webkit-fill-available;
}

body {
  min-height: -webkit-fill-available;
}

/* General styling */
.main-title {
  margin: 2rem 0;
  font-size: 2.5rem;
  color: #333;
}

.effective-date {
  margin-bottom: 2rem;
  color: #666;
}

.intro-text {
  margin-bottom: 3rem;
  line-height: 1.6;
  max-width: 800px;
}

/* Section styling */
.policy-section {
  margin: 3rem 0;
  padding: 0 1rem;
}

.policy-section h2 {
  margin-bottom: 1.5rem;
  color: #333;
}

.subsection {
  margin: 2rem 0;
  padding-left: 1rem;
}

.subsection h3 {
  margin-bottom: 1rem;
  color: #444;
}

/* Lists */
.bullet-list {
  margin-left: 1.5rem;
  line-height: 1.8;
}

/* Contact section */
.contact-section {
  background-color: #f8f8f8;
  padding: 2rem;
  margin: 3rem 0;
  border-radius: 8px;
}

.contact-details ul {
  list-style: none;
  padding: 0;
}

.contact-details li {
  margin: 1rem 0;
}

/* Footer */
.policy-footer {
  margin-top: 4rem;
  padding: 2rem 0;
  border-top: 1px solid #eee;
  text-align: center;
}

/* Google maps info window  */
.gm-style-iw-c {
  padding: 0 !important;
  margin: 0 !important;
  box-shadow: none;
  border-radius: 12px !important;
}
.gm-style-iw-d {
  overflow: auto !important;
}
@layer base {
  * {
    @apply border-border;
  }
}

.watermark-overlay {
  position: absolute;
  inset: 0;
  z-index: 10;
}

.watermark-overlay::before {
  content: "";
  overflow: hidden;
  position: absolute;
  bottom: 12px;
  right: 12px;
  width: 96px;
  height: 36px;
  font-size: 1px;
  color: rgba(255, 255, 255, 0.8);
  white-space: nowrap;
  /* transform: rotate(-45deg); */
  background-image: url("/property-seekers-main-logo.png");
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  align-items: end;
  justify-content: end;
  flex-wrap: wrap;
  padding: 8px;
  /* line-height: 4rem; */
  pointer-events: none;
  user-select: none;
  opacity: 0.8;
}

.grecaptcha-badge {
  visibility: hidden;
}
