"use client"
import { BaseLayout } from "@/types/base";
import SearchMap from "./search-map";
import { useEffect, useState } from "react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight, List, Map } from "lucide-react";
import { useTranslations } from "next-intl";
import FilterHeader from "./filter-header";
import SearchResultCount from "./search-result-count";
import { useSeekersSearchMapUtil, ViewMode } from "@/stores/seekers-search-map-utils";
import MainContentLayout from "@/components/seekers-content-layout/main-content-layout";
import useSearchParamWrapper from "@/hooks/use-search-param-wrapper";
import { filterTitles } from "@/lib/constanta/constant";

export default function SearchLayoutContent({ children, conversions }: BaseLayout & { conversions: { [key: string]: number } }) {
  const t = useTranslations("seeker")
  const { createQueryString, createMultipleQueryString, searchParams } = useSearchParamWrapper()

  const [isShowList, setShowList] = useState<boolean>(true)
  const setViewMode = useSeekersSearchMapUtil(state => state.setViewMode)
  const setHighlightedListing = useSeekersSearchMapUtil(state => state.setHighlightedListing)

  useEffect(() => {
    const viewMode = searchParams.get(filterTitles.viewMode) as ViewMode | null
    if (!viewMode || viewMode == "list") {
      setShowList(true)
      setViewMode("list")

    } else {
      setShowList(false)
      setViewMode("map")
    }
  }, [searchParams, setViewMode])
  const handleSetViewMode = () => {
    const viewMode = searchParams.get(filterTitles.viewMode) as ViewMode | null
    if (viewMode == "map") {
      createQueryString(filterTitles.viewMode, "list")
      setShowList(false)
    } else {
      createQueryString(filterTitles.viewMode, "map")
      setShowList(true)
    }
    setHighlightedListing(null)
    window.scrollTo({ top: 0 })
  }
  return <MainContentLayout className="space-y-0 max-sm:px-0">
    {/* Desktop view */}
    <div className="hidden md:flex mb-6 mx-auto h-full">
      <div className={cn("", isShowList ? "flex-1 " : "w-0 hidden")}>
        {children}
      </div>
      <div className={cn("sticky  md:top-[182px] lg:top-[208px] xl:top-[220px] h-[calc(100vh-220px)]", isShowList ? "min-w-[28%]" : "w-full")}>
        <div className="w-full h-full relative">
          <Button
            className="absolute z-20 top-4 left-4"
            size={"icon"}
            onClick={() => {
              handleSetViewMode()


            }}
            variant={"outline"}>{isShowList ?
              <ChevronLeft className="!w-5 !h-5 !text-seekers-primary" />
              :
              <ChevronRight className="!w-5 !h-5 !text-seekers-primary" />}
          </Button>
          <SearchMap lat={-8.535522079444435} lng={115.2228026405029} conversions={conversions} />
        </div>
      </div>
    </div>

    {/* Mobile view */}
    <div className="md:hidden isolate">
      {isShowList ?
        <>
          <div className=" p-4 sticky space-y-2 top-[140px] z-10 bg-white">
            <FilterHeader conversions={conversions} />
            <SearchResultCount />
          </div>
          {children}
        </>
        :
        <div className={cn("sticky h-[calc(100vh-176px)]")}>
          <div className="w-full h-full relative">
            <SearchMap lat={-8.535522079444435} lng={115.2228026405029} conversions={conversions} />
          </div>
        </div>
      }
      <button className="inline-flex items-center gap-2 sticky z-10 bottom-4 left-1/2 -translate-x-1/2 text-white bg-seekers-text p-4 rounded-full"
        onClick={() => handleSetViewMode()}>
        {
          isShowList ? <>
            <Map /> <span>{t('cta.maps')}</span>
          </> : <>
            <List /> <span>{t('cta.list')}</span>
          </>
        }
      </button>
    </div>
  </MainContentLayout>
}