"use client";
import {
  packageFeature,
  PackageFeatureDetailType,
  packages,
  PackagesType,
  Subscription,
} from "@/core/domain/subscription/subscription";
import { useUserStore } from "@/stores/user.store";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";

export interface PlanSelector {
  name: string;
  value: PackagesType;
  id: PackagesType;
  monthlyPrice: number;
  threeMonthlyPrice: number;
}
const free: Subscription = {
  name: packages.free,
  currency: "eur",
  priceVariant: [
    {
      price: 0,
      priceId: "free-monthly",
      cycleUnit: "month",
      cycleCount: 1,
      currency: "eur",
    },
    {
      price: 0,
      priceId: "free-three-monthly",
      cycleUnit: "month",
      cycleCount: 3,
      currency: "eur",
    },
  ],
  productId: "free-product",
};

export default function useSubscription(
  subscriptionDetail: Subscription[] | null
) {
  const t = useTranslations("seeker");
  const quarterlyDiscount = 0.85; //15% OFF
  const membership = useUserStore((state) => state.seekers.accounts.membership);
  const [availablePlan, setAvailablePlan] = useState<Subscription[]>([]);

  useEffect(() => {
    const setupAvailablePlan = () => {
      const levelOne = subscriptionDetail?.find(
        (item) => item.name == packages.finder
      );
      const levelTwo = subscriptionDetail?.find(
        (item) => item.name == packages.archiver
      );
      if (!levelOne || !levelTwo) return;
      switch (membership) {
        case packages.free:
          return setAvailablePlan([free, levelOne, levelTwo]);
        case packages.finder:
        case packages.archiver:
          return setAvailablePlan([levelOne, levelTwo]);
        default:
          return setAvailablePlan([free, levelOne, levelTwo]);
      }
    };
    setupAvailablePlan();
  }, [membership, subscriptionDetail]);

  const handleSetPackage = (val: PackagesType) => {
    if (val == packages.free) return freePackageFeature;
    if (val == packages.finder) return basicPackageFeature;
    if (val == packages.archiver) return premiumPackageFeature;

    return freePackageFeature;
  };

  const calculateQuarterlySavings = (monthlyPrice: number) => {
    const quarterlyPrice = monthlyPrice * 3 * 0.85; // 15% discount
    const savings = monthlyPrice * 3 - quarterlyPrice;
    return Math.round(savings);
  };

  const handleDowngradeLevelLabel = (val: PackagesType) => {
    if (val == packages.archiver) return packages.finder;
    return "";
  };
  const handleUpgradeLevelLabel = (val: PackagesType) => {
    if (val == packages.finder) return packages.finder;
    if (val == packages.archiver) return packages.archiver;
    return "";
  };
  const freePackageFeature: PackageFeatureDetailType = {
    [packageFeature.contactOwner]: false,
    [packageFeature.photos]:
      t("misc.limitedAccess") + " " + t("misc.ofThreePicture"),
    [packageFeature.mapLocation]: t("misc.limitedAccess"),
    [packageFeature.favoriteProperties]: t("misc.notPossibleToFavorite"),
    [packageFeature.advanceAndSaveFilter]: true,
    [packageFeature.savedListing]: false,
    // [packageFeature.listingUpdate]: false,
    // [packageFeature.priceHistory]: false,
    // [packageFeature.virtualTour]: false,
    // [packageFeature.neighborhoodInsights]: false,
    // [packageFeature.comparisonTool]: false,
    // [packageFeature.expertConsultant]: false,
    // [packageFeature.offMarketListing]: false,
    // [packageFeature.transactions]: false,
  };
  const basicPackageFeature: PackageFeatureDetailType = {
    [packageFeature.contactOwner]: t("subscription.benefit.fivePerWeeks"),
    [packageFeature.photos]: t("misc.fullAccess") + t("misc.seeAllPhoto"),
    [packageFeature.mapLocation]: t("misc.fullAccess"),
    [packageFeature.favoriteProperties]: true,
    [packageFeature.advanceAndSaveFilter]: true,
    [packageFeature.savedListing]: t("misc.saveProperty", { count: 20 }),
    // [packageFeature.listingUpdate]: t("subscription.benefit.weeklyEmailUpdate"),
    // [packageFeature.priceHistory]: t(
    //   "subscription.benefit.priceHistory.lastThreeMonth"
    // ),
    // [packageFeature.virtualTour]: t(
    //   "subscription.benefit.virtualTour.limitedAccess"
    // ),
    // [packageFeature.neighborhoodInsights]: t(
    //   "subscription.benefit.neightborInsight.detail"
    // ),
    // [packageFeature.comparisonTool]: t(
    //   "subscription.benefit.comparisonTool.compareUpToThreeProperty"
    // ),
    // [packageFeature.expertConsultant]: t(
    //   "subscription.benefit.expertConsultant.email"
    // ),
    // [packageFeature.offMarketListing]: t("misc.limitedAccess"),
    // [packageFeature.transactions]: t("misc.limitedAccess"),
  };
  const premiumPackageFeature: PackageFeatureDetailType = {
    [packageFeature.contactOwner]: t("subscription.benefit.fifteenPerWeeks"),
    [packageFeature.photos]: t("misc.fullAccess") + t("misc.seeAllPhoto"),
    [packageFeature.mapLocation]:
      t("misc.fullAccess") + t("misc.seeExactLocation"),
    [packageFeature.favoriteProperties]: t("misc.saveProperty", { count: 20 }),
    [packageFeature.advanceAndSaveFilter]: true,
    [packageFeature.savedListing]: t("misc.unlimited"),
    // [packageFeature.listingUpdate]: t(
    //   "subscription.benefit.realtimeNotification"
    // ),
    // [packageFeature.priceHistory]: t(
    //   "subscription.benefit.priceHistory.fullPriceHistory"
    // ),
    // [packageFeature.virtualTour]: t(
    //   "subscription.benefit.virtualTour.fullAccess"
    // ),
    // [packageFeature.neighborhoodInsights]: t(
    //   "subscription.benefit.neightborInsight.marketTrendAndPrediction"
    // ),
    // [packageFeature.comparisonTool]: t(
    //   "subscription.benefit.comparisonTool.compareUnlimitedProperty"
    // ),
    // [packageFeature.expertConsultant]: t(
    //   "subscription.benefit.expertConsultant.realtime"
    // ),
    // [packageFeature.offMarketListing]: t("misc.fullAccess"),
    // [packageFeature.transactions]: t("misc.fullAccess"),
  };
  const packageFeatureLabel = [
    {
      id: packageFeature.contactOwner,
      label: t("setting.subscriptionStatus.subscription.features.optionOne"),
    },
    {
      id: packageFeature.photos,
      label: t("setting.subscriptionStatus.subscription.features.optionTwo"),
    },
    {
      id: packageFeature.mapLocation,
      label: t("setting.subscriptionStatus.subscription.features.optionThree"),
    },
    {
      id: packageFeature.favoriteProperties,
      label: t(
        "setting.subscriptionStatus.subscription.features.optionFourteen"
      ),
    },
    {
      id: packageFeature.advanceAndSaveFilter,
      label: t("setting.subscriptionStatus.subscription.features.optionFour"),
    },
    // {
    //   id: packageFeature.listingUpdate,
    //   label: t("setting.subscriptionStatus.subscription.features.optionSix"),
    // },
    // {
    //   id: packageFeature.priceHistory,
    //   label: t("setting.subscriptionStatus.subscription.features.optionSeven"),
    // },
    // {
    //   id: packageFeature.virtualTour,
    //   label: t("setting.subscriptionStatus.subscription.features.optionEight"),
    // },
    // {
    //   id: packageFeature.neighborhoodInsights,
    //   label: t("setting.subscriptionStatus.subscription.features.optionNine"),
    // },
    // {
    //   id: packageFeature.comparisonTool,
    //   label: t("setting.subscriptionStatus.subscription.features.optionTen"),
    // },
    // {
    //   id: packageFeature.expertConsultant,
    //   label: t("setting.subscriptionStatus.subscription.features.optionEleven"),
    // },
    // {
    //   id: packageFeature.offMarketListing,
    //   label: t("setting.subscriptionStatus.subscription.features.optionTwelve"),
    // },
    // {
    //   id: packageFeature.transactions,
    //   label: t(
    //     "setting.subscriptionStatus.subscription.features.optionThirdteen"
    //   ),
    // },
  ];

  return {
    handleSetPackage,
    availablePlan,
    packageFeatureLabel,
    calculateQuarterlySavings,
    quarterlyDiscount,
    handleUpgradeLevelLabel,
    handleDowngradeLevelLabel,
  };
}
