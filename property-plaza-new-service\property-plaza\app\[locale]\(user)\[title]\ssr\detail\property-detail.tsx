import BathupIcon from "@/components/icons/property-detail/Bathtub.svg"
import BedroomIcon from "@/components/icons/property-detail/Bedrooms.svg"
import BuildingSizeIcon from "@/components/icons/property-detail/Building Size.svg"
import GardenSizeIcon from "@/components/icons/property-detail/Garden Size.svg"
import LandSizeIcon from "@/components/icons/property-detail/Land Size.svg"
import ViewIcon from "@/components/icons/property-detail/View.svg"
import YearsofBuildIcon from "@/components/icons/property-detail/Year of build.svg"
import GarbageFeeIcon from "@/components/icons/property-detail/Garbage fees.svg"
import WifiIcon from "@/components/icons/property-detail/Wifi.svg"
import WaterIcon from "@/components/icons/property-detail/Water.svg"
import ElectricityIcon from "@/components/icons/property-detail/Electricity (kW).svg"
import VillageFeeIcon from "@/components/icons/property-detail/Amount of years and months 2.svg"
import FurnishingStatusIcon from "@/components/icons/property-detail/Furnished-Unfurnished.svg"
import ParkingStatusIcon from "@/components/icons/property-detail/Private-shared Parking.svg"
import LivingStatusIcon from "@/components/icons/property-detail/Closed-Open living.svg"
import PoolStatusIcon from "@/components/icons/property-detail/Private-shared Pool.svg"
import { BasicInformation, Feature } from "@/core/domain/listing/listing";
import PropertyDescription from "./property-description";
import AmenitiesFormatter from "../../amenities-formatter";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { School } from "lucide-react"

export default function PropertyDetail({ title, description, sellingPoints, detail, features }: {
  title: string,
  description?: string,
  sellingPoints: string[],
  detail: BasicInformation,
  features: Feature
}) {
  const t = useTranslations("seeker")
  return <div className="w-full space-y-12">
    <div className="space-y-6">
      <h1 className="text-3xl font-bold text-seekers-text">{title}</h1>
    </div>
    <div className="space-y-6 !mt-6">
      {
        description && <PropertyDescription description={description} />
      }
    </div>
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-seekers-text">{t('listing.detail.popularFacility.title')}</h2>
      <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
        {features.sellingPoints.map((item, idx) => <AmenitiesFormatter key={idx} amenities={item} />
        )}
      </div>
    </div>
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-seekers-text">{t('listing.detail.mainFacilities.title')}</h2>
      <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
        <>
          {
            detail.bathroomTotal! > 0 &&
            <ItemWithIcon
              title={t('propertyDetail.totalBathroom.title')}
              iconUrl={BathupIcon}
              content={<p>
                {t('listing.detail.mainFacilities.bathroom.content', { count: detail.bathroomTotal })}</p>
              } />
          }
          {
            detail.bedroomTotal! > 0 &&
            <ItemWithIcon
              title={t('propertyDetail.totalBedroom.title')}
              iconUrl={BedroomIcon} content={<p> {t('listing.detail.mainFacilities.bedroom', { count: detail.bedroomTotal })}</p>} />
          }
          {detail.buildingSize! > 0 &&
            <ItemWithIcon
              title={t('propertyDetail.buildingSize.title')}
              iconUrl={BuildingSizeIcon} content={<div className="absolute -top-1 left-8">

                <p>{detail.buildingSize} m<span className="align-super">2</span> {t('propertyDetail.buildingSize.title')}</p>
              </div>
              } />
          }
          {detail.cascoStatus &&
            <ItemWithIcon
              title={t('propertyDetail.cascoStatus.title')}
              customIcon={<School className="w-6 h-6" />}
              content={<p>{t('listing.detail.mainFacilities.shellAndCore')}</p>} />
          }
          {detail.gardenSize! > 0 &&
            <ItemWithIcon
              title={t('propertyDetail.gardenSize.title')}
              iconUrl={GardenSizeIcon}
              content={
                <div className="absolute -top-1 left-8">
                  <p>{detail.gardenSize} m<span className="align-super">2</span> {t('listing.detail.mainFacilities.gardenSize')}</p>
                </div>
              } />
          }
          {detail.landSize! > 0 &&
            <ItemWithIcon
              title={t('propertyDetail.landSize.title')}
              iconUrl={LandSizeIcon}
              content={
                <div className="absolute -top-1 left-8">
                  <p>{detail.landSize} m<span className="align-super">2</span> {t('listing.detail.mainFacilities.landSize')}</p>
                </div>
              } />
          }
          {detail.propertyOfView! &&
            <ItemWithIcon
              title={t('propertyDetail.viewOfProperty.title')}
              iconUrl={ViewIcon}
              content={<p>{detail.propertyOfView} {t('listing.detail.mainFacilities.view')} </p>} />
          }
          {detail.yearsOfBuilding! &&
            <ItemWithIcon
              title={t('propertyDetail.yearsOfBuild.title')}
              iconUrl={YearsofBuildIcon}
              content={<p>{t('listing.detail.mainFacilities.yearsOfBuild')} {detail.yearsOfBuilding}</p>} />
          }
        </>
      </div>
    </div>
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-seekers-text">{t('listing.detail.rentalPricingIncluding.title')}</h2>
      <div className="grid grid-cols-2 md:grid-cols-3  gap-6">
        <>
          {
            detail.villageFee! &&
            <ItemWithIcon
              title={t('propertyDetail.villageFee.title')}
              iconUrl={VillageFeeIcon}
              content={
                <p>
                  {t('listing.detail.rentalPricingIncluding.villageFeeIncluded')}
                </p>
              }
            />
          }
          {
            detail.wifiService! > 0 &&
            <ItemWithIcon
              title={t('propertyDetail.wifi.title')}
              iconUrl={WifiIcon}
              content={
                <p>
                  {
                    detail.wifiService
                  }
                  {
                    detail.typeWifiSpeed
                  } {t('listing.detail.rentalPricingIncluding.wifi')}</p>} />
          }
          {
            detail.garbageFee! &&
            <ItemWithIcon
              title={t('propertyDetail.garbageFee.title')}
              iconUrl={GarbageFeeIcon}
              content={<p> {t('listing.detail.rentalPricingIncluding.gardenFeeIncluded')}</p>} />
          }
          {
            detail.waterFee! &&
            <ItemWithIcon
              iconUrl={WaterIcon}
              title={t('propertyDetail.waterFee.title')}
              content={<p> {t('listing.detail.rentalPricingIncluding.waterFeeIncluded')}</p>} />
          }

          {
            features.electricity > 0 &&
            <ItemWithIcon
              iconUrl={ElectricityIcon}
              title={t('propertyDetail.electricity.title')}
              content={<p>{features.electricity} {t('detail.rentalPricingIncluding.electricity')} </p>} />
          }
          {
            features.amenities &&
            features.amenities.map((item, idx) =>
              <AmenitiesFormatter key={idx} amenities={item} />
            )
          }
          {
            features.furnishingOption &&
            <ItemWithIcon iconUrl={FurnishingStatusIcon} title={t('propertyDetail.furnishingStatus.title')} content={<p> {features.furnishingOption == "FURNISHED" ?
              t('listing.detail.mainFacilities.furnishing.furnished')
              : t('detail.mainFacilities.furnishing.unfurnished')
            } </p>} />
          }
          {
            features.livingOption &&
            <ItemWithIcon iconUrl={LivingStatusIcon} title={t('propertyDetail.livingStatus.title')} content={
              <p>
                {features.livingOption == "CLOSED_LIVING" ?
                  t('listing.detail.mainFacilities.living.privateLiving')
                  : features.livingOption == "SHARED_LIVING" ?
                    t('listing.detail.mainFacilities.living.sharedLiving')
                    : t("listing.detail.mainFacilities.living.openLiving")
                }
              </p>
            }
            />
          }
          {
            (features.parkingOption == "PRIVATE" || features.parkingOption == "PUBLIC") &&
            <ItemWithIcon iconUrl={ParkingStatusIcon} title={t('propertyDetail.parking.title')} content={<p>
              {
                features.parkingOption == "PUBLIC" ?
                  t('listing.detail.mainFacilities.parking.publicParking')
                  : t('listing.detail.mainFacilities.parking.privateParking')
              }
            </p>}
            />
          }
          {
            features.poolOption == "AVAILABLE" &&
            <ItemWithIcon title={t('listing.detail.mainFacilities.pool.available')} iconUrl={PoolStatusIcon} content={
              <>
                <p> {t('listing.detail.mainFacilities.pool.available')} </p>
              </>
            }
            />
          }
        </>
      </div>
    </div>
  </div>
}

function ItemWithIcon({ iconUrl, content, customIcon, title }: { iconUrl?: string, content: React.ReactNode, customIcon?: React.ReactNode, title: string }) {
  return <div className="flex gap-2 text-sm text-seekers-text max-h-6 items-center relative">
    {customIcon ||
      <Image loading="lazy" src={iconUrl || ""} alt="" width={24} height={24} className={"w-6 h-6"} title={title} />
    }
    {content}
  </div>
}