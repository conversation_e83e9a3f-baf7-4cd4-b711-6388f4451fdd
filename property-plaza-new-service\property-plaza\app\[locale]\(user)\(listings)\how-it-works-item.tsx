"use client"
import { cn } from "@/lib/utils";
import React, { ComponentProps } from "react";
import { motion } from "framer-motion"
export interface HowItWorksItemProps extends ComponentProps<"div"> {
  icon: React.ReactNode,
  title: string,
  description: string
}
export default function HowItWorksItem({ icon, title, description, ...rest }: HowItWorksItemProps) {
  return <motion.div
    whileHover={{ y: -5 }}
    className={cn("w-full p-6 px-8 bg-seekers-background text-seekers-text rounded-[32px] border-seekers-text-lighter border-2 space-y-3 hover:shadow-xl transition-all cursor-pointer", rest.className)}>
    <div className="flex gap-1.5 items-center">
      {icon}
      <h3 className="font-bold text-sm tracking-[0.5%]">{title}</h3>
    </div>
    <p className="font-medium text-xs">{description}</p>
  </motion.div>
}