"use client"

import React, { useState } from "react"
import ImageDetailCarousel from "./image-detail-carousel"
import { IMAGE_DETAIL_BUTTON_ID } from "../utils/use-image-gallery"

export default function ImageDetailCarouselTrigger({ imagesUrl, isSubscribe }: { imagesUrl: string[], isSubscribe?: boolean }) {
  const [open, setOpen] = useState(false)

  return <>
    <button
      className="hidden"
      id={IMAGE_DETAIL_BUTTON_ID}
      onClick={() => setOpen(true)}
    />
    <ImageDetailCarousel
      isSubscribe={isSubscribe}
      imagesUrl={imagesUrl}
      open={open}
      setOpen={setOpen}
    />
  </>
}